%% 构件特定处理模块
% 针对墩柱、盖梁、梁片三种构件的差异化吊装策略

function [trajectory, strategy] = planComponentSpecificTrajectory(component_type, ...
    initial_path, crane, config)
    % 根据构件类型规划特定轨迹
    % 输入:
    %   component_type - 构件类型 'column', 'cap_beam', 'beam'
    %   initial_path - 初始路径
    %   crane - 架桥机参数
    %   config - 配置参数
    % 输出:
    %   trajectory - 优化后的轨迹
    %   strategy - 吊装策略参数
    
    switch component_type
        case 'column'
            [trajectory, strategy] = planColumnInstallation(initial_path, crane, config);
        case 'cap_beam'
            [trajectory, strategy] = planCapBeamInstallation(initial_path, crane, config);
        case 'beam'
            [trajectory, strategy] = planBeamInstallation(initial_path, crane, config);
        otherwise
            error('未知的构件类型: %s', component_type);
    end
end

function [trajectory, strategy] = planColumnInstallation(path, crane, config)
    % 墩柱安装策略
    % 前后小车同步吊起，同步纵向移动，后小车先放，后小车脱钩，前小车下放
    
    strategy = struct();
    strategy.type = 'column';
    strategy.phases = {'sync_lift', 'sync_move', 'rear_lower', 'rear_detach', 'front_lower'};
    
    % 初始化轨迹结构
    trajectory = initializeTrajectory(path, crane);
    
    % 阶段1: 同步吊起
    phase1 = generateSyncLiftPhase(trajectory, crane, config);
    
    % 阶段2: 同步纵向移动
    phase2 = generateSyncMovePhase(phase1, path, crane, config);
    
    % 阶段3: 后小车下放
    phase3 = generateRearLowerPhase(phase2, crane, config);
    
    % 阶段4: 后小车脱钩
    phase4 = generateRearDetachPhase(phase3, crane, config);
    
    % 阶段5: 前小车下放
    phase5 = generateFrontLowerPhase(phase4, crane, config);
    
    % 组装完整轨迹
    trajectory = assemblePhases([phase1, phase2, phase3, phase4, phase5]);
    
    % 优化过渡段
    trajectory = optimizeTransitions(trajectory, crane);
end

function [trajectory, strategy] = planCapBeamInstallation(path, crane, config)
    % 盖梁安装策略
    % 前小车吊起，前小车纵向移动，二级吊具90°旋转，前小车下放
    
    strategy = struct();
    strategy.type = 'cap_beam';
    strategy.phases = {'front_lift', 'front_move', 'rotate_90', 'front_lower'};
    strategy.rotation_required = true;
    
    % 初始化轨迹结构
    trajectory = initializeTrajectory(path, crane);
    
    % 阶段1: 前小车吊起
    phase1 = generateFrontLiftPhase(trajectory, crane, config);
    
    % 阶段2: 前小车纵向移动
    phase2 = generateFrontMovePhase(phase1, path, crane, config);
    
    % 阶段3: 二级吊具90°旋转
    phase3 = generateRotationPhase(phase2, pi/2, crane, config);
    
    % 阶段4: 前小车下放
    phase4 = generateFrontLowerPhase(phase3, crane, config);
    
    % 组装完整轨迹
    trajectory = assemblePhases([phase1, phase2, phase3, phase4]);
    
    % 添加旋转信息
    trajectory.rotation_angle = generateRotationProfile(trajectory, strategy);
end

function [trajectory, strategy] = planBeamInstallation(path, crane, config)
    % 梁片安装策略
    % 前小车吊起，前小车纵移，后小车吊起，同步纵向移动，同步下放
    
    strategy = struct();
    strategy.type = 'beam';
    strategy.phases = {'front_lift', 'front_move', 'rear_lift', 'sync_move', 'sync_lower'};
    
    % 初始化轨迹结构
    trajectory = initializeTrajectory(path, crane);
    
    % 阶段1: 前小车吊起
    phase1 = generateFrontLiftPhase(trajectory, crane, config);
    
    % 阶段2: 前小车纵移
    phase2 = generateFrontMovePhase(phase1, path(1:floor(end/3), :), crane, config);
    
    % 阶段3: 后小车吊起
    phase3 = generateRearLiftPhase(phase2, crane, config);
    
    % 阶段4: 同步纵向移动
    phase4 = generateSyncMovePhase(phase3, path(floor(end/3)+1:end, :), crane, config);
    
    % 阶段5: 同步下放
    phase5 = generateSyncLowerPhase(phase4, crane, config);
    
    % 组装完整轨迹
    trajectory = assemblePhases([phase1, phase2, phase3, phase4, phase5]);
end

%% 各阶段生成函数

function trajectory = initializeTrajectory(path, crane)
    % 初始化轨迹结构
    trajectory = struct();
    trajectory.time = [];
    trajectory.hook1 = [];
    trajectory.hook2 = [];
    trajectory.load_center = [];
    trajectory.load_orientation = [];
    trajectory.phase_markers = [];
    
    % 初始位置
    start_pos = path(1, 1:3);
    trajectory.hook1_init = start_pos + [0, -crane.hook_distance/2, 0];
    trajectory.hook2_init = start_pos + [0, crane.hook_distance/2, 0];
end

function phase = generateSyncLiftPhase(prev_trajectory, crane, config)
    % 生成同步吊起阶段
    
    lift_height = config.lift_height;
    lift_time = lift_height / crane.hoist.max_vel;
    dt = config.qp.dt;
    
    n_steps = ceil(lift_time / dt);
    phase = struct();
    phase.time = (0:n_steps-1)' * dt;
    
    % 速度规划 - 梯形剖面
    [z_profile, vz_profile] = generateTrapezoidalProfile(0, lift_height, ...
        crane.hoist.max_vel, crane.hoist.max_acc, n_steps);
    
    % 两个吊钩同步上升
    phase.hook1 = zeros(n_steps, 6);
    phase.hook2 = zeros(n_steps, 6);
    
    for i = 1:n_steps
        % 吊钩1
        phase.hook1(i, :) = [
            prev_trajectory.hook1_init(1:2), ...
            prev_trajectory.hook1_init(3) + z_profile(i), ...
            0, 0, vz_profile(i)
        ];
        
        % 吊钩2
        phase.hook2(i, :) = [
            prev_trajectory.hook2_init(1:2), ...
            prev_trajectory.hook2_init(3) + z_profile(i), ...
            0, 0, vz_profile(i)
        ];
    end
    
    phase.type = 'sync_lift';
end

function phase = generateSyncMovePhase(prev_phase, path, crane, config)
    % 生成同步移动阶段
    
    % 提取移动路径
    start_pos = [prev_phase.hook1(end, 1:2), prev_phase.hook1(end, 3)];
    end_pos = path(end, 1:3);
    
    % 规划XY平面路径
    [xy_path, path_length] = planXYPath(start_pos(1:2), end_pos(1:2), path);
    
    % 时间和步数
    move_time = path_length / crane.main_cart.max_vel;
    dt = config.qp.dt;
    n_steps = ceil(move_time / dt);
    
    phase = struct();
    phase.time = prev_phase.time(end) + (1:n_steps)' * dt;
    
    % 路径参数化
    s = linspace(0, 1, n_steps)';
    
    phase.hook1 = zeros(n_steps, 6);
    phase.hook2 = zeros(n_steps, 6);
    
    for i = 1:n_steps
        % 插值位置
        pos = interpPath(xy_path, s(i));
        
        % 计算速度
        if i > 1
            vel = (pos - interpPath(xy_path, s(i-1))) / dt;
        else
            vel = [0, 0];
        end
        
        % 吊钩1
        phase.hook1(i, :) = [
            pos, prev_phase.hook1(end, 3), ...
            vel, 0
        ];
        
        % 吊钩2
        phase.hook2(i, :) = [
            pos + [0, crane.hook_distance], prev_phase.hook2(end, 3), ...
            vel, 0
        ];
    end
    
    phase.type = 'sync_move';
end

function phase = generateRearLowerPhase(prev_phase, crane, config)
    % 生成后小车下放阶段
    
    lower_height = config.rear_lower_height;
    lower_time = lower_height / crane.hoist.max_vel;
    dt = config.qp.dt;
    
    n_steps = ceil(lower_time / dt);
    phase = struct();
    phase.time = prev_phase.time(end) + (1:n_steps)' * dt;
    
    % 速度规划
    [z_profile, vz_profile] = generateTrapezoidalProfile(0, -lower_height, ...
        crane.hoist.max_vel, crane.hoist.max_acc, n_steps);
    
    phase.hook1 = zeros(n_steps, 6);
    phase.hook2 = zeros(n_steps, 6);
    
    for i = 1:n_steps
        % 前吊钩保持不动
        phase.hook1(i, :) = prev_phase.hook1(end, :);
        phase.hook1(i, 4:6) = 0; % 速度为0
        
        % 后吊钩下降
        phase.hook2(i, :) = prev_phase.hook2(end, :);
        phase.hook2(i, 3) = prev_phase.hook2(end, 3) + z_profile(i);
        phase.hook2(i, 6) = vz_profile(i);
    end
    
    phase.type = 'rear_lower';
end

function phase = generateRearDetachPhase(prev_phase, crane, config)
    % 生成后小车脱钩阶段
    
    % 脱钩动作时间
    detach_time = config.detach_time;
    dt = config.qp.dt;
    n_steps = ceil(detach_time / dt);
    
    phase = struct();
    phase.time = prev_phase.time(end) + (1:n_steps)' * dt;
    
    phase.hook1 = repmat(prev_phase.hook1(end, :), n_steps, 1);
    phase.hook2 = repmat(prev_phase.hook2(end, :), n_steps, 1);
    
    % 标记脱钩事件
    phase.events = struct('type', 'detach', 'hook', 'rear', 'time', phase.time(end/2));
    phase.type = 'rear_detach';
end

function phase = generateRotationPhase(prev_phase, angle, crane, config)
    % 生成旋转阶段
    
    rotation_time = abs(angle) / crane.rotator.max_vel;
    dt = config.qp.dt;
    n_steps = ceil(rotation_time / dt);
    
    phase = struct();
    phase.time = prev_phase.time(end) + (1:n_steps)' * dt;
    
    % 角度规划
    [angle_profile, omega_profile] = generateTrapezoidalProfile(0, angle, ...
        crane.rotator.max_vel, crane.rotator.max_acc, n_steps);
    
    % 旋转中心
    center = prev_phase.hook1(end, 1:3);
    
    phase.hook1 = zeros(n_steps, 6);
    phase.hook2 = zeros(n_steps, 6);
    phase.rotation = zeros(n_steps, 1);
    
    for i = 1:n_steps
        % 前吊钩不动
        phase.hook1(i, :) = prev_phase.hook1(end, :);
        phase.hook1(i, 4:6) = 0;
        
        % 负载旋转
        phase.rotation(i) = angle_profile(i);
        
        % 后吊钩跟随旋转（如果是双吊钩操作）
        if config.dual_hook_rotation
            % 计算旋转后的位置
            R = [cos(angle_profile(i)), -sin(angle_profile(i)), 0;
                 sin(angle_profile(i)), cos(angle_profile(i)), 0;
                 0, 0, 1];
            
            rel_pos = prev_phase.hook2(end, 1:3) - center;
            new_pos = (R * rel_pos')' + center;
            
            phase.hook2(i, 1:3) = new_pos;
            
            % 计算速度
            if i > 1
                phase.hook2(i, 4:6) = (new_pos - phase.hook2(i-1, 1:3)) / dt;
            end
        else
            phase.hook2(i, :) = prev_phase.hook2(end, :);
            phase.hook2(i, 4:6) = 0;
        end
    end
    
    phase.type = 'rotate';
end

%% 辅助函数

function [profile, velocity] = generateTrapezoidalProfile(start_val, end_val, ...
    v_max, a_max, n_steps)
    % 生成梯形速度剖面
    
    total_dist = abs(end_val - start_val);
    direction = sign(end_val - start_val);
    
    % 计算加速、匀速、减速时间
    t_acc = v_max / a_max;
    d_acc = 0.5 * a_max * t_acc^2;
    
    if 2 * d_acc > total_dist
        % 三角剖面
        t_acc = sqrt(total_dist / a_max);
        t_const = 0;
        t_dec = t_acc;
    else
        % 梯形剖面
        d_const = total_dist - 2 * d_acc;
        t_const = d_const / v_max;
        t_dec = t_acc;
    end
    
    total_time = t_acc + t_const + t_dec;
    dt = total_time / n_steps;
    
    profile = zeros(n_steps, 1);
    velocity = zeros(n_steps, 1);
    
    for i = 1:n_steps
        t = (i - 1) * dt;
        
        if t <= t_acc
            % 加速阶段
            profile(i) = start_val + direction * 0.5 * a_max * t^2;
            velocity(i) = direction * a_max * t;
        elseif t <= t_acc + t_const
            % 匀速阶段
            profile(i) = start_val + direction * (d_acc + v_max * (t - t_acc));
            velocity(i) = direction * v_max;
        else
            % 减速阶段
            t_dec_elapsed = t - t_acc - t_const;
            profile(i) = end_val - direction * 0.5 * a_max * (t_dec - t_dec_elapsed)^2;
            velocity(i) = direction * v_max * (1 - t_dec_elapsed / t_dec);
        end
    end
end

function [xy_path, path_length] = planXYPath(start_xy, end_xy, reference_path)
    % 规划XY平面路径
    
    if nargin < 3 || isempty(reference_path)
        % 直线路径
        xy_path = [start_xy; end_xy];
        path_length = norm(end_xy - start_xy);
    else
        % 使用参考路径
        xy_path = reference_path(:, 1:2);
        
        % 确保起点和终点
        if ~isequal(xy_path(1, :), start_xy)
            xy_path = [start_xy; xy_path];
        end
        if ~isequal(xy_path(end, :), end_xy)
            xy_path = [xy_path; end_xy];
        end
        
        % 计算路径长度
        segments = diff(xy_path);
        path_length = sum(vecnorm(segments, 2, 2));
    end
end

function pos = interpPath(path, s)
    % 路径插值
    
    if s <= 0
        pos = path(1, :);
        return;
    elseif s >= 1
        pos = path(end, :);
        return;
    end
    
    % 计算累积长度
    segments = diff(path);
    seg_lengths = vecnorm(segments, 2, 2);
    cum_lengths = [0; cumsum(seg_lengths)];
    total_length = cum_lengths(end);
    
    % 目标长度
    target_length = s * total_length;
    
    % 找到对应段
    idx = find(cum_lengths >= target_length, 1) - 1;
    if idx < 1
        idx = 1;
    end
    
    % 段内插值
    seg_start_length = cum_lengths(idx);
    seg_length = seg_lengths(idx);
    t = (target_length - seg_start_length) / seg_length;
    
    pos = path(idx, :) * (1 - t) + path(idx + 1, :) * t;
end

function trajectory = assemblePhases(phases)
    % 组装各阶段轨迹
    
    trajectory = struct();
    trajectory.time = [];
    trajectory.hook1 = [];
    trajectory.hook2 = [];
    trajectory.phase_markers = [];
    trajectory.events = [];
    
    for i = 1:length(phases)
        phase = phases(i);
        
        % 添加时间和轨迹数据
        trajectory.time = [trajectory.time; phase.time];
        trajectory.hook1 = [trajectory.hook1; phase.hook1];
        trajectory.hook2 = [trajectory.hook2; phase.hook2];
        
        % 记录阶段标记
        trajectory.phase_markers(end+1) = struct(...
            'type', phase.type, ...
            'start_idx', length(trajectory.time) - length(phase.time) + 1, ...
            'end_idx', length(trajectory.time));
        
        % 记录事件
        if isfield(phase, 'events')
            trajectory.events = [trajectory.events, phase.events];
        end
        
        % 旋转信息
        if isfield(phase, 'rotation')
            if ~isfield(trajectory, 'rotation')
                trajectory.rotation = zeros(length(trajectory.time) - length(phase.time), 1);
            end
            trajectory.rotation = [trajectory.rotation; phase.rotation];
        end
    end
    
    % 计算负载中心
    trajectory.load_center = (trajectory.hook1(:, 1:3) + trajectory.hook2(:, 1:3)) / 2;
end

function trajectory = optimizeTransitions(trajectory, crane)
    % 优化阶段过渡
    
    % 识别过渡点
    transition_indices = [];
    for i = 1:length(trajectory.phase_markers)-1
        transition_indices(end+1) = trajectory.phase_markers(i).end_idx;
    end
    
    % 平滑过渡
    window_size = 5; % 过渡窗口
    
    for idx = transition_indices
        if idx - window_size < 1 || idx + window_size > length(trajectory.time)
            continue;
        end
        
        % 提取过渡段
        window_idx = (idx - window_size):(idx + window_size);
        
        % 速度连续性优化
        trajectory.hook1(window_idx, 4:6) = smoothdata(trajectory.hook1(window_idx, 4:6), ...
            'gaussian', 3);
        trajectory.hook2(window_idx, 4:6) = smoothdata(trajectory.hook2(window_idx, 4:6), ...
            'gaussian', 3);
        
        % 重新积分位置
        for i = window_idx(2:end)
            dt = trajectory.time(i) - trajectory.time(i-1);
            trajectory.hook1(i, 1:3) = trajectory.hook1(i-1, 1:3) + ...
                                       trajectory.hook1(i-1, 4:6) * dt;
            trajectory.hook2(i, 1:3) = trajectory.hook2(i-1, 1:3) + ...
                                       trajectory.hook2(i-1, 4:6) * dt;
        end
    end
    
    % 更新负载中心
    trajectory.load_center = (trajectory.hook1(:, 1:3) + trajectory.hook2(:, 1:3)) / 2;
end

function rotation_profile = generateRotationProfile(trajectory, strategy)
    % 生成旋转角度剖面
    
    n_steps = length(trajectory.time);
    rotation_profile = zeros(n_steps, 1);
    
    if ~strategy.rotation_required
        return;
    end
    
    % 找到旋转阶段
    for i = 1:length(trajectory.phase_markers)
        if strcmp(trajectory.phase_markers(i).type, 'rotate')
            start_idx = trajectory.phase_markers(i).start_idx;
            end_idx = trajectory.phase_markers(i).end_idx;
            
            if isfield(trajectory, 'rotation')
                rotation_profile(start_idx:end_idx) = trajectory.rotation(start_idx:end_idx);
            end
        end
    end
end

%% 安全检查函数

function safe = checkInstallationSafety(trajectory, crane, env)
    % 检查安装过程安全性
    
    safe = true;
    safety_report = struct();
    
    % 检查工作空间
    safety_report.workspace = checkWorkspaceLimits(trajectory, crane);
    
    % 检查碰撞
    safety_report.collision = checkTrajectoryCollisions(trajectory, env);
    
    % 检查动力学约束
    safety_report.dynamics = checkDynamicsSafety(trajectory, crane);
    
    % 检查负载稳定性
    safety_report.stability = checkLoadStability(trajectory, crane);
    
    % 综合判断
    safe = safety_report.workspace && ~safety_report.collision && ...
           safety_report.dynamics && safety_report.stability;
    
    if ~safe
        warning('安装过程存在安全隐患:');
        disp(safety_report);
    end
end

function safe = checkWorkspaceLimits(trajectory, crane)
    % 检查工作空间限制
    
    % 吊钩1
    hook1_min = min(trajectory.hook1(:, 1:3));
    hook1_max = max(trajectory.hook1(:, 1:3));
    
    safe1 = all(hook1_min >= crane.hook1.workspace(:, 1)') && ...
            all(hook1_max <= crane.hook1.workspace(:, 2)');
    
    % 吊钩2
    hook2_min = min(trajectory.hook2(:, 1:3));
    hook2_max = max(trajectory.hook2(:, 1:3));
    
    safe2 = all(hook2_min >= crane.hook2.workspace(:, 1)') && ...
            all(hook2_max <= crane.hook2.workspace(:, 2)');
    
    safe = safe1 && safe2;
end

function collision = checkTrajectoryCollisions(trajectory, env)
    % 检查轨迹碰撞
    
    collision = false;
    
    for i = 1:size(trajectory.hook1, 1)
        % 简化的负载包围盒
        load_center = trajectory.load_center(i, :);
        
        for j = 1:length(env.obstacles)
            if checkLoadObstacleCollision(load_center, env.obstacles(j))
                collision = true;
                return;
            end
        end
    end
end

function collision = checkLoadObstacleCollision(load_pos, obstacle)
    % 检查负载与障碍物碰撞
    
    % 简化为点检测
    dist = norm(load_pos - obstacle.center);
    safety_margin = 2.0; % 安全余量
    
    collision = dist < (norm(obstacle.size) / 2 + safety_margin);
end

function safe = checkDynamicsSafety(trajectory, crane)
    % 检查动力学安全性
    
    dt = mean(diff(trajectory.time));
    
    % 速度检查
    v1_max = max(vecnorm(trajectory.hook1(:, 4:6), 2, 2));
    v2_max = max(vecnorm(trajectory.hook2(:, 4:6), 2, 2));
    
    v_limit = min([crane.main_cart.max_vel, crane.small_cart.max_vel, crane.hoist.max_vel]);
    
    safe_vel = (v1_max < v_limit * 1.1) && (v2_max < v_limit * 1.1);
    
    % 加速度检查
    a1 = diff(trajectory.hook1(:, 4:6)) / dt;
    a2 = diff(trajectory.hook2(:, 4:6)) / dt;
    
    a1_max = max(vecnorm(a1, 2, 2));
    a2_max = max(vecnorm(a2, 2, 2));
    
    a_limit = min([crane.main_cart.max_acc, crane.small_cart.max_acc, crane.hoist.max_acc]);
    
    safe_acc = (a1_max < a_limit * 1.2) && (a2_max < a_limit * 1.2);
    
    safe = safe_vel && safe_acc;
end

function stable = checkLoadStability(trajectory, crane)
    % 检查负载稳定性
    
    % 估算最大摆角
    g = 9.81;
    max_lateral_acc = max(vecnorm(trajectory.hook1(:, 4:5), 2, 2));
    estimated_swing = atan(max_lateral_acc / g);
    
    % 检查吊钩同步性
    hook_dist = vecnorm(trajectory.hook1(:, 1:3) - trajectory.hook2(:, 1:3), 2, 2);
    sync_error = std(hook_dist);
    
    stable = (estimated_swing < deg2rad(10)) && (sync_error < 0.5);
end