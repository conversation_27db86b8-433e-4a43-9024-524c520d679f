%% 工具函数与异常处理模块
% 系统通用工具函数和异常处理机制

%% 数据结构定义与验证
function validateSystemInput(config, env, crane)
    % 验证系统输入参数
    
    % 验证配置
    required_config_fields = {'component_type', 'ga', 'rrt', 'qp', 'gpr'};
    for i = 1:length(required_config_fields)
        if ~isfield(config, required_config_fields{i})
            error('配置缺少必需字段: %s', required_config_fields{i});
        end
    end
    
    % 验证环境
    if ~isfield(env, 'workspace') || ~isfield(env, 'obstacles')
        error('环境模型不完整');
    end
    
    % 验证架桥机模型
    validateCraneModel(crane);
    
    % 检查构件类型
    valid_types = {'column', 'cap_beam', 'beam'};
    if ~ismember(config.component_type, valid_types)
        error('无效的构件类型: %s', config.component_type);
    end
end

function validateCraneModel(crane)
    % 验证架桥机模型完整性
    
    required_fields = {
        'main_cart', 'small_cart', 'hoist', 'rotator', ...
        'hook1', 'hook2', 'load', 'hook_distance'
    };
    
    for i = 1:length(required_fields)
        if ~isfield(crane, required_fields{i})
            error('架桥机模型缺少字段: %s', required_fields{i});
        end
    end
    
    % 验证运动学参数
    mechanisms = {'main_cart', 'small_cart', 'hoist', 'rotator'};
    for i = 1:length(mechanisms)
        mech = crane.(mechanisms{i});
        if ~isfield(mech, 'max_vel') || ~isfield(mech, 'max_acc')
            error('%s缺少速度/加速度限制', mechanisms{i});
        end
        if mech.max_vel <= 0 || mech.max_acc <= 0
            error('%s的速度/加速度限制必须为正值', mechanisms{i});
        end
    end
end

%% 异常处理与回退策略
function result = safeExecute(func, fallback_func, varargin)
    % 安全执行函数，失败时使用回退策略
    
    try
        result = func(varargin{:});
    catch ME
        warning(E.identifier, '%s', E.message)
        
        if nargin >= 2 && ~isempty(fallback_func)
            try
                fprintf('尝试回退策略...\n');
                result = fallback_func(varargin{:});
            catch ME2
                error('回退策略也失败: %s', ME2.message);
            end
        else
            rethrow(ME);
        end
    end
end

function trajectory = fallbackTrajectoryPlanning(start_pos, goal_pos, crane)
    % 简单的回退轨迹规划
    
    fprintf('使用简单直线轨迹作为回退方案\n');
    
    % 直线插值
    n_points = 50;
    t = linspace(0, 1, n_points)';
    
    trajectory = struct();
    trajectory.time = t * 10; % 假设10秒完成
    
    % 线性插值位置
    positions = (1 - t) * start_pos + t * goal_pos;
    
    % 梯形速度剖面
    velocities = zeros(n_points, 3);
    for i = 2:n_points
        velocities(i, :) = (positions(i, :) - positions(i-1, :)) / ...
                          (trajectory.time(i) - trajectory.time(i-1));
    end
    
    % 生成双吊钩轨迹
    trajectory.hook1 = [positions - [0, crane.hook_distance/2, 0], velocities];
    trajectory.hook2 = [positions + [0, crane.hook_distance/2, 0], velocities];
    trajectory.load_center = positions;
end

%% 数据格式转换
function standardized = standardizeTrajectory(raw_trajectory)
    % 标准化轨迹数据格式
    
    standardized = struct();
    
    % 确保必需字段
    required_fields = {'time', 'hook1', 'hook2', 'load_center'};
    for i = 1:length(required_fields)
        if isfield(raw_trajectory, required_fields{i})
            standardized.(required_fields{i}) = raw_trajectory.(required_fields{i});
        else
            error('轨迹缺少必需字段: %s', required_fields{i});
        end
    end
    
    % 验证数据维度
    n_steps = length(standardized.time);
    if size(standardized.hook1, 1) ~= n_steps || ...
       size(standardized.hook2, 1) ~= n_steps
        error('轨迹数据维度不一致');
    end
    
    % 确保速度信息
    if size(standardized.hook1, 2) == 3
        % 只有位置，计算速度
        standardized.hook1 = addVelocityInfo(standardized.hook1, standardized.time);
        standardized.hook2 = addVelocityInfo(standardized.hook2, standardized.time);
    end
    
    % 添加额外信息
    standardized.duration = standardized.time(end) - standardized.time(1);
    standardized.n_steps = n_steps;
    
    % 计算轨迹统计
    standardized.stats = computeTrajectoryStats(standardized);
end

function data_with_vel = addVelocityInfo(positions, time)
    % 为位置数据添加速度信息
    
    n = size(positions, 1);
    velocities = zeros(n, 3);
    
    % 中心差分计算速度
    for i = 2:n-1
        dt_prev = time(i) - time(i-1);
        dt_next = time(i+1) - time(i);
        
        vel_prev = (positions(i, :) - positions(i-1, :)) / dt_prev;
        vel_next = (positions(i+1, :) - positions(i, :)) / dt_next;
        
        velocities(i, :) = (vel_prev + vel_next) / 2;
    end
    
    % 边界处理
    velocities(1, :) = velocities(2, :);
    velocities(end, :) = velocities(end-1, :);
    
    data_with_vel = [positions, velocities];
end

function stats = computeTrajectoryStats(trajectory)
    % 计算轨迹统计信息
    
    stats = struct();
    
    % 路径长度
    path1 = sum(vecnorm(diff(trajectory.hook1(:, 1:3)), 2, 2));
    path2 = sum(vecnorm(diff(trajectory.hook2(:, 1:3)), 2, 2));
    stats.path_length = struct('hook1', path1, 'hook2', path2, ...
                              'average', (path1 + path2) / 2);
    
    % 速度统计
    v1 = vecnorm(trajectory.hook1(:, 4:6), 2, 2);
    v2 = vecnorm(trajectory.hook2(:, 4:6), 2, 2);
    
    stats.velocity = struct();
    stats.velocity.max = max([max(v1), max(v2)]);
    stats.velocity.mean = mean([v1; v2]);
    stats.velocity.std = std([v1; v2]);
    
    % 同步性统计
    sync_error = vecnorm(trajectory.hook1(:, 1:3) - trajectory.hook2(:, 1:3) - ...
                        repmat([0, -15, 0], size(trajectory.hook1, 1), 1), 2, 2);
    stats.sync_error = struct('max', max(sync_error), ...
                             'mean', mean(sync_error), ...
                             'std', std(sync_error));
end

%% 日志和监控
function logger = initializeLogger(log_file)
    % 初始化日志系统
    
    logger = struct();
    logger.file = log_file;
    logger.start_time = datetime('now');
    logger.entries = {};
    
    % 创建日志文件
    fid = fopen(log_file, 'w');
    if fid == -1
        warning('无法创建日志文件，使用控制台输出');
        logger.file = [];
    else
        fprintf(fid, '双吊桥架桥机优化系统日志\n');
        fprintf(fid, '开始时间: %s\n', char(logger.start_time));
        fprintf(fid, '='*50 + '\n');
        fclose(fid);
    end
end

function logMessage(logger, level, message, varargin)
    % 记录日志消息
    
    timestamp = datetime('now');
    formatted_msg = sprintf(message, varargin{:});
    
    log_entry = struct();
    log_entry.time = timestamp;
    log_entry.level = level;
    log_entry.message = formatted_msg;
    
    % 添加到日志
    if isfield(logger, 'entries')
        logger.entries{end+1} = log_entry;
    end
    
    % 格式化输出
    log_line = sprintf('[%s] %s: %s\n', ...
        datestr(timestamp, 'HH:MM:SS'), upper(level), formatted_msg);
    
    % 控制台输出
    switch level
        case 'error'
            fprintf(2, log_line); % 错误流
        case 'warning'
            fprintf(1, log_line);
        otherwise
            fprintf(1, log_line);
    end
    
    % 文件输出
    if ~isempty(logger.file)
        fid = fopen(logger.file, 'a');
        if fid ~= -1
            fprintf(fid, log_line);
            fclose(fid);
        end
    end
end

%% 性能监控
function monitor = createPerformanceMonitor()
    % 创建性能监控器
    
    monitor = struct();
    monitor.timers = containers.Map();
    monitor.counters = containers.Map();
    monitor.memory_usage = [];
end

function startTimer(monitor, timer_name)
    % 启动计时器
    monitor.timers(timer_name) = tic;
end

function elapsed = stopTimer(monitor, timer_name)
    % 停止计时器并返回经过时间
    
    if isKey(monitor.timers, timer_name)
        elapsed = toc(monitor.timers(timer_name));
        remove(monitor.timers, timer_name);
    else
        warning('计时器不存在: %s', timer_name);
        elapsed = NaN;
    end
end

function incrementCounter(monitor, counter_name, value)
    % 增加计数器
    
    if nargin < 3
        value = 1;
    end
    
    if isKey(monitor.counters, counter_name)
        monitor.counters(counter_name) = monitor.counters(counter_name) + value;
    else
        monitor.counters(counter_name) = value;
    end
end

function report = generatePerformanceReport(monitor)
    % 生成性能报告
    
    report = struct();
    
    % 计时信息
    report.timers = struct();
    timer_keys = keys(monitor.timers);
    for i = 1:length(timer_keys)
        key = timer_keys{i};
        report.timers.(key) = toc(monitor.timers(key));
    end
    
    % 计数信息
    report.counters = struct();
    counter_keys = keys(monitor.counters);
    for i = 1:length(counter_keys)
        key = counter_keys{i};
        report.counters.(key) = monitor.counters(key);
    end
    
    % 内存使用
    [user, sys] = memory;
    report.memory = struct();
    report.memory.used = user.MemUsedMATLAB / 1e9; % GB
    report.memory.available = user.MemAvailableAllArrays / 1e9; % GB
end

%% 配置管理
function config = loadConfiguration(config_file)
    % 从文件加载配置
    
    if exist(config_file, 'file')
        try
            config = jsondecode(fileread(config_file));
            fprintf('配置加载成功: %s\n', config_file);
        catch
            warning('配置文件解析失败，使用默认配置');
            config = getDefaultConfig();
        end
    else
        warning('配置文件不存在，使用默认配置');
        config = getDefaultConfig();
    end
    
    % 验证和补充配置
    config = validateAndCompleteConfig(config);
end

function saveConfiguration(config, config_file)
    % 保存配置到文件
    
    try
        json_str = jsonencode(config, 'PrettyPrint', true);
        fid = fopen(config_file, 'w');
        fprintf(fid, '%s', json_str);
        fclose(fid);
        fprintf('配置保存成功: %s\n', config_file);
    catch ME
        error('配置保存失败: %s', ME.message);
    end
end

function config = getDefaultConfig()
    % 获取默认配置
    
    config = struct();
    
    % 基本设置
    config.component_type = 'beam';
    config.optimization_mode = 'balanced'; % 'fast', 'balanced', 'quality'
    
    % GA参数
    config.ga.population_size = 100;
    config.ga.max_generations = 50;
    config.ga.crossover_rate = 0.8;
    config.ga.mutation_rate = 0.1;
    
    % RRT参数
    config.rrt.max_iter = 1000;
    config.rrt.step_size = 0.5;
    config.rrt.goal_bias = 0.1;
    config.rrt.neighbor_radius = 1.0;
    
    % QP参数
    config.qp.dt = 0.1;
    config.qp.horizon = 100;
    config.qp.weight_time = 0.4;
    config.qp.weight_energy = 0.3;
    config.qp.weight_stability = 0.3;
    
    % GPR参数
    config.gpr.update_interval = 10;
    config.gpr.n_samples = 50;
    config.gpr.kernel = 'squaredexponential';
    
    % 安全参数
    config.safety.max_swing_angle = deg2rad(10);
    config.safety.collision_margin = 2.0;
    config.safety.sync_tolerance = 0.5;
end

function config = validateAndCompleteConfig(config)
    % 验证并补充配置
    
    default_config = getDefaultConfig();
    
    % 递归合并配置
    config = mergeStructs(default_config, config);
    
    % 特殊验证
    if config.ga.population_size < 20
        warning('种群规模过小，设置为最小值20');
        config.ga.population_size = 20;
    end
    
    if config.qp.dt > 0.5
        warning('时间步长过大，设置为0.5');
        config.qp.dt = 0.5;
    end
end

function merged = mergeStructs(default, user)
    % 递归合并结构体
    
    merged = default;
    
    if ~isstruct(user)
        return;
    end
    
    fields = fieldnames(user);
    for i = 1:length(fields)
        field = fields{i};
        
        if isfield(default, field) && isstruct(default.(field)) && isstruct(user.(field))
            % 递归合并子结构体
            merged.(field) = mergeStructs(default.(field), user.(field));
        else
            % 直接赋值
            merged.(field) = user.(field);
        end
    end
end

%% 数据导出
function exportOptimizationResults(results, output_dir)
    % 导出优化结果
    
    if ~exist(output_dir, 'dir')
        mkdir(output_dir);
    end
    
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    
    % 导出轨迹数据
    trajectory_file = fullfile(output_dir, sprintf('trajectory_%s.mat', timestamp));
    save(trajectory_file, '-struct', results.trajectory);
    
    % 导出性能指标
    metrics_file = fullfile(output_dir, sprintf('metrics_%s.csv', timestamp));
    exportMetricsToCSV(results.performance, metrics_file);
    
    % 导出控制指令
    commands_file = fullfile(output_dir, sprintf('commands_%s.csv', timestamp));
    exportControlCommands(results.control_commands, commands_file);
    
    % 生成报告
    report_file = fullfile(output_dir, sprintf('report_%s.txt', timestamp));
    generateTextReport(results, report_file);
    
    fprintf('结果已导出至: %s\n', output_dir);
end

function exportMetricsToCSV(metrics, filename)
    % 导出性能指标到CSV
    
    % 扁平化结构体
    flat_metrics = flattenStruct(metrics);
    
    % 转换为表格
    metric_names = fieldnames(flat_metrics);
    metric_values = struct2cell(flat_metrics);
    
    T = table(metric_names, metric_values, ...
              'VariableNames', {'Metric', 'Value'});
    
    writetable(T, filename);
end

function flat = flattenStruct(s, prefix)
    % 扁平化嵌套结构体
    
    if nargin < 2
        prefix = '';
    end
    
    flat = struct();
    fields = fieldnames(s);
    
    for i = 1:length(fields)
        field = fields{i};
        value = s.(field);
        
        if isstruct(value)
            % 递归处理
            sub_flat = flattenStruct(value, [prefix, field, '_']);
            sub_fields = fieldnames(sub_flat);
            for j = 1:length(sub_fields)
                flat.(sub_fields{j}) = sub_flat.(sub_fields{j});
            end
        else
            % 直接赋值
            if isempty(prefix)
                flat.(field) = value;
            else
                flat.([prefix, field]) = value;
            end
        end
    end
end

function generateTextReport(results, filename)
    % 生成文本报告
    
    fid = fopen(filename, 'w');
    
    fprintf(fid, '双吊桥架桥机优化结果报告\n');
    fprintf(fid, '生成时间: %s\n', datestr(now));
    fprintf(fid, '='*60 + '\n\n');
    
    fprintf(fid, '优化配置:\n');
    fprintf(fid, '  构件类型: %s\n', results.config.component_type);
    fprintf(fid, '  优化模式: %s\n', results.config.optimization_mode);
    fprintf(fid, '  GA代数: %d\n', results.config.ga.max_generations);
    fprintf(fid, '\n');
    
    fprintf(fid, '性能指标:\n');
    fprintf(fid, '  作业时间: %.2f 秒\n', results.performance.trajectory_metrics.time);
    fprintf(fid, '  总能耗: %.2f MJ\n', results.performance.trajectory_metrics.energy / 1e6);
    fprintf(fid, '  最大摆角: %.2f 度\n', ...
            results.performance.dynamics_metrics.max_swing_angle * 180/pi);
    fprintf(fid, '  稳定性评分: %.2f\n', ...
            results.performance.dynamics_metrics.stability_score);
    fprintf(fid, '\n');
    
    fprintf(fid, '轨迹统计:\n');
    fprintf(fid, '  路径长度: %.2f m\n', results.trajectory.stats.path_length.average);
    fprintf(fid, '  最大速度: %.2f m/s\n', results.trajectory.stats.velocity.max);
    fprintf(fid, '  同步误差: %.3f m (平均)\n', results.trajectory.stats.sync_error.mean);
    
    fclose(fid);
end