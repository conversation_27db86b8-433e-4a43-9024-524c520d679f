%% 实时监控与控制界面
% 双吊桥架桥机优化系统的图形用户界面

classdef realtime_monitor_gui < matlab.apps.AppBase
    
    % 属性定义
    properties (Access = public)
        UIFigure                   matlab.ui.Figure
        GridLayout                 matlab.ui.container.GridLayout
        
        % 左侧面板 - 控制
        ControlPanel               matlab.ui.container.Panel
        ComponentTypeDropDown      matlab.ui.control.DropDown
        OptimizationModeDropDown   matlab.ui.control.DropDown
        StartButton                matlab.ui.control.Button
        StopButton                 matlab.ui.control.Button
        PauseButton                matlab.ui.control.Button
        LoadConfigButton           matlab.ui.control.Button
        SaveConfigButton           matlab.ui.control.Button
        
        % 参数调整
        ParametersPanel            matlab.ui.container.Panel
        PopulationSizeSpinner      matlab.ui.control.Spinner
        MaxGenerationsSpinner      matlab.ui.control.Spinner
        TimeWeightSlider           matlab.ui.control.Slider
        EnergyWeightSlider         matlab.ui.control.Slider
        StabilityWeightSlider      matlab.ui.control.Slider
        
        % 中央面板 - 3D可视化
        VisualizationPanel         matlab.ui.container.Panel
        View3DAxes                 matlab.ui.control.UIAxes
        AnimationSpeedSlider       matlab.ui.control.Slider
        ShowTrajectoryCheckBox     matlab.ui.control.CheckBox
        ShowObstaclesCheckBox      matlab.ui.control.CheckBox
        
        % 右侧面板 - 监控
        MonitoringPanel            matlab.ui.container.Panel
        ProgressGauge              matlab.ui.control.LinearGauge
        GenerationLabel            matlab.ui.control.Label
        BestFitnessLabel           matlab.ui.control.Label
        TimeElapsedLabel           matlab.ui.control.Label
        
        % 底部面板 - 图表
        ChartsPanel                matlab.ui.container.Panel
        ConvergenceAxes            matlab.ui.control.UIAxes
        ParetoAxes                 matlab.ui.control.UIAxes
        MetricsAxes                matlab.ui.control.UIAxes
        
        % 状态栏
        StatusLabel                matlab.ui.control.Label
        
        % 数据存储
        Config                     struct
        OptimizationData           struct
        IsRunning                  logical
        IsPaused                   logical
        Timer                      timer
    end
    
    % 私有属性
    properties (Access = private)
        OptimizationThread         % 优化线程
        UpdateTimer                % 更新计时器
        AnimationHandle            % 动画句柄
        Logger                     % 日志记录器
        Monitor                    % 性能监控器
    end
    
    % 方法
    methods (Access = private)
        
        function startupFcn(app)
            % 应用启动函数
            
            % 初始化配置
            app.Config = initializeSystemConfig();
            
            % 初始化数据结构
            app.OptimizationData = struct();
            app.OptimizationData.history = [];
            app.OptimizationData.current_generation = 0;
            app.OptimizationData.best_fitness = [Inf, Inf, -Inf];
            
            % 初始化状态
            app.IsRunning = false;
            app.IsPaused = false;
            
            % 初始化日志和监控
            app.Logger = initializeLogger('optimization_log.txt');
            app.Monitor = createPerformanceMonitor();
            
            % 设置初始UI状态
            app.StopButton.Enable = 'off';
            app.PauseButton.Enable = 'off';
            
            % 创建更新计时器
            app.UpdateTimer = timer(...
                'ExecutionMode', 'fixedRate', ...
                'Period', 0.5, ...
                'TimerFcn', @(~,~)app.updateDisplay());
            
            % 设置3D视图
            setup3DView(app);
            
            % 加载默认场景
            loadDefaultScenario(app);
            
            % 显示欢迎信息
            app.StatusLabel.Text = '系统就绪，请选择构件类型并点击开始';
            
            logMessage(app.Logger, 'info', 'GUI启动成功');
        end
        
        function closeRequestFcn(app)
            % 关闭请求函数
            
            % 停止优化
            if app.IsRunning
                stopOptimization(app);
            end
            
            % 停止计时器
            if ~isempty(app.UpdateTimer) && isvalid(app.UpdateTimer)
                stop(app.UpdateTimer);
                delete(app.UpdateTimer);
            end
            
            % 保存日志
            logMessage(app.Logger, 'info', 'GUI关闭');
            
            % 删除应用
            delete(app);
        end
        
        function StartButtonPushed(app, ~)
            % 开始按钮回调
            
            if app.IsRunning
                return;
            end
            
            % 更新配置
            updateConfigFromUI(app);
            
            % 验证配置
            try
                validateSystemInput(app.Config, [], []);
            catch ME
                uialert(app.UIFigure, ME.message, '配置错误');
                return;
            end
            
            % 设置UI状态
            app.IsRunning = true;
            app.StartButton.Enable = 'off';
            app.StopButton.Enable = 'on';
            app.PauseButton.Enable = 'on';
            
            % 清空历史数据
            app.OptimizationData.history = [];
            app.OptimizationData.start_time = tic;
            
            % 启动更新计时器
            start(app.UpdateTimer);
            
            % 启动优化线程
            app.OptimizationThread = parfeval(@runOptimizationAsync, 1, app.Config);
            
            app.StatusLabel.Text = '优化运行中...';
            logMessage(app.Logger, 'info', '开始优化，构件类型: %s', app.Config.component_type);
        end
        
        function StopButtonPushed(app, ~)
            % 停止按钮回调
            stopOptimization(app);
        end
        
        function PauseButtonPushed(app, ~)
            % 暂停按钮回调
            
            if ~app.IsRunning
                return;
            end
            
            app.IsPaused = ~app.IsPaused;
            
            if app.IsPaused
                app.PauseButton.Text = '继续';
                app.StatusLabel.Text = '优化已暂停';
            else
                app.PauseButton.Text = '暂停';
                app.StatusLabel.Text = '优化运行中...';
            end
        end
        
        function LoadConfigButtonPushed(app, ~)
            % 加载配置按钮回调
            
            [file, path] = uigetfile('*.json', '选择配置文件');
            if isequal(file, 0)
                return;
            end
            
            try
                app.Config = loadConfiguration(fullfile(path, file));
                updateUIFromConfig(app);
                app.StatusLabel.Text = sprintf('配置已加载: %s', file);
            catch ME
                uialert(app.UIFigure, ME.message, '加载失败');
            end
        end
        
        function SaveConfigButtonPushed(app, ~)
            % 保存配置按钮回调
            
            [file, path] = uiputfile('*.json', '保存配置文件', 'config.json');
            if isequal(file, 0)
                return;
            end
            
            try
                updateConfigFromUI(app);
                saveConfiguration(app.Config, fullfile(path, file));
                app.StatusLabel.Text = sprintf('配置已保存: %s', file);
            catch ME
                uialert(app.UIFigure, ME.message, '保存失败');
            end
        end
        
        function WeightSliderChanged(app, ~)
            % 权重滑块变化回调
            
            % 归一化权重
            total = app.TimeWeightSlider.Value + ...
                    app.EnergyWeightSlider.Value + ...
                    app.StabilityWeightSlider.Value;
            
            if total > 0
                app.TimeWeightSlider.Value = app.TimeWeightSlider.Value / total;
                app.EnergyWeightSlider.Value = app.EnergyWeightSlider.Value / total;
                app.StabilityWeightSlider.Value = app.StabilityWeightSlider.Value / total;
            end
        end
        
        function updateDisplay(app)
            % 更新显示
            
            if ~app.IsRunning
                return;
            end
            
            % 检查优化线程状态
            if ~isempty(app.OptimizationThread) && isvalid(app.OptimizationThread)
                if strcmp(app.OptimizationThread.State, 'finished')
                    % 获取结果
                    try
                        results = fetchOutputs(app.OptimizationThread);
                        handleOptimizationComplete(app, results);
                    catch ME
                        app.StatusLabel.Text = sprintf('优化失败: %s', ME.message);
                        stopOptimization(app);
                    end
                    return;
                end
            end
            
            % 更新进度
            if ~isempty(app.OptimizationData.history)
                current_gen = app.OptimizationData.current_generation;
                max_gen = app.Config.ga.max_generations;
                
                app.ProgressGauge.Value = (current_gen / max_gen) * 100;
                app.GenerationLabel.Text = sprintf('代数: %d / %d', current_gen, max_gen);
                
                % 更新最佳适应度
                if ~isempty(app.OptimizationData.best_fitness)
                    app.BestFitnessLabel.Text = sprintf(...
                        '最优: 时间=%.1fs, 能耗=%.1fMJ, 稳定性=%.2f', ...
                        app.OptimizationData.best_fitness(1), ...
                        app.OptimizationData.best_fitness(2) / 1e6, ...
                        -app.OptimizationData.best_fitness(3));
                end
            end
            
            % 更新时间
            if isfield(app.OptimizationData, 'start_time')
                elapsed = toc(app.OptimizationData.start_time);
                app.TimeElapsedLabel.Text = sprintf('用时: %.1f秒', elapsed);
            end
            
            % 更新图表
            updateCharts(app);
        end
        
        function updateCharts(app)
            % 更新图表
            
            if isempty(app.OptimizationData.history)
                return;
            end
            
            history = app.OptimizationData.history;
            
            % 收敛曲线
            cla(app.ConvergenceAxes);
            hold(app.ConvergenceAxes, 'on');
            
            generations = 1:length(history.best_fitness);
            plot(app.ConvergenceAxes, generations, history.best_fitness(:, 1), ...
                 'b-', 'LineWidth', 2);
            plot(app.ConvergenceAxes, generations, history.mean_fitness(:, 1), ...
                 'r--', 'LineWidth', 1.5);
            
            xlabel(app.ConvergenceAxes, '代数');
            ylabel(app.ConvergenceAxes, '作业时间 (s)');
            title(app.ConvergenceAxes, '适应度收敛');
            legend(app.ConvergenceAxes, {'最优', '平均'}, 'Location', 'best');
            grid(app.ConvergenceAxes, 'on');
            
            % Pareto前沿
            if isfield(history, 'pareto_front') && ~isempty(history.pareto_front)
                cla(app.ParetoAxes);
                scatter(app.ParetoAxes, ...
                        history.pareto_front(:, 1), ...
                        history.pareto_front(:, 2), ...
                        50, -history.pareto_front(:, 3), 'filled');
                
                xlabel(app.ParetoAxes, '时间 (s)');
                ylabel(app.ParetoAxes, '能耗 (J)');
                title(app.ParetoAxes, 'Pareto前沿');
                colorbar(app.ParetoAxes);
                grid(app.ParetoAxes, 'on');
            end
        end
        
        function update3DVisualization(app, trajectory)
            % 更新3D可视化
            
            if ~app.ShowTrajectoryCheckBox.Value
                return;
            end
            
            cla(app.View3DAxes);
            hold(app.View3DAxes, 'on');
            
            % 绘制轨迹
            plot3(app.View3DAxes, ...
                  trajectory.hook1(:, 1), ...
                  trajectory.hook1(:, 2), ...
                  trajectory.hook1(:, 3), ...
                  'b-', 'LineWidth', 2, 'DisplayName', '吊钩1');
            
            plot3(app.View3DAxes, ...
                  trajectory.hook2(:, 1), ...
                  trajectory.hook2(:, 2), ...
                  trajectory.hook2(:, 3), ...
                  'r-', 'LineWidth', 2, 'DisplayName', '吊钩2');
            
            plot3(app.View3DAxes, ...
                  trajectory.load_center(:, 1), ...
                  trajectory.load_center(:, 2), ...
                  trajectory.load_center(:, 3), ...
                  'k--', 'LineWidth', 1.5, 'DisplayName', '负载中心');
            
            % 绘制障碍物
            if app.ShowObstaclesCheckBox.Value
                drawObstaclesInAxes(app.View3DAxes, app.OptimizationData.env);
            end
            
            % 设置视图
            xlabel(app.View3DAxes, 'X (m)');
            ylabel(app.View3DAxes, 'Y (m)');
            zlabel(app.View3DAxes, 'Z (m)');
            title(app.View3DAxes, '轨迹可视化');
            legend(app.View3DAxes, 'Location', 'best');
            grid(app.View3DAxes, 'on');
            axis(app.View3DAxes, 'equal');
            view(app.View3DAxes, 45, 30);
        end
        
        function stopOptimization(app)
            % 停止优化
            
            app.IsRunning = false;
            app.IsPaused = false;
            
            % 取消优化线程
            if ~isempty(app.OptimizationThread) && isvalid(app.OptimizationThread)
                cancel(app.OptimizationThread);
            end
            
            % 停止更新计时器
            if ~isempty(app.UpdateTimer) && isvalid(app.UpdateTimer)
                stop(app.UpdateTimer);
            end
            
            % 更新UI状态
            app.StartButton.Enable = 'on';
            app.StopButton.Enable = 'off';
            app.PauseButton.Enable = 'off';
            app.PauseButton.Text = '暂停';
            
            app.StatusLabel.Text = '优化已停止';
            logMessage(app.Logger, 'info', '优化停止');
        end
        
        function handleOptimizationComplete(app, results)
            % 处理优化完成
            
            % 停止优化
            stopOptimization(app);
            
            % 保存结果
            app.OptimizationData.results = results;
            
            % 更新显示
            app.StatusLabel.Text = '优化完成！';
            
            % 显示最终轨迹
            if isfield(results, 'trajectory')
                update3DVisualization(app, results.trajectory);
            end
            
            % 显示结果对话框
            showResultsDialog(app, results);
            
            % 自动导出结果
            exportOptimizationResults(results, 'output');
            
            logMessage(app.Logger, 'info', '优化完成，结果已导出');
        end
        
        function showResultsDialog(app, results)
            % 显示结果对话框
            
            msg = sprintf(['优化完成！\n\n', ...
                          '最优解性能:\n', ...
                          '- 作业时间: %.2f 秒\n', ...
                          '- 能耗: %.2f MJ\n', ...
                          '- 稳定性评分: %.2f\n\n', ...
                          '结果已保存至output文件夹'], ...
                          results.performance.trajectory_metrics.time, ...
                          results.performance.trajectory_metrics.energy / 1e6, ...
                          results.performance.dynamics_metrics.stability_score);
            
            uialert(app.UIFigure, msg, '优化完成', 'Icon', 'success');
        end
        
        function updateConfigFromUI(app)
            % 从UI更新配置
            
            app.Config.component_type = app.ComponentTypeDropDown.Value;
            app.Config.optimization_mode = app.OptimizationModeDropDown.Value;
            
            app.Config.ga.population_size = app.PopulationSizeSpinner.Value;
            app.Config.ga.max_generations = app.MaxGenerationsSpinner.Value;
            
            app.Config.qp.weight_time = app.TimeWeightSlider.Value;
            app.Config.qp.weight_energy = app.EnergyWeightSlider.Value;
            app.Config.qp.weight_stability = app.StabilityWeightSlider.Value;
        end
        
        function updateUIFromConfig(app)
            % 从配置更新UI
            
            app.ComponentTypeDropDown.Value = app.Config.component_type;
            app.OptimizationModeDropDown.Value = app.Config.optimization_mode;
            
            app.PopulationSizeSpinner.Value = app.Config.ga.population_size;
            app.MaxGenerationsSpinner.Value = app.Config.ga.max_generations;
            
            app.TimeWeightSlider.Value = app.Config.qp.weight_time;
            app.EnergyWeightSlider.Value = app.Config.qp.weight_energy;
            app.StabilityWeightSlider.Value = app.Config.qp.weight_stability;
        end
        
        function setup3DView(app)
            % 设置3D视图
            
            app.View3DAxes.XGrid = 'on';
            app.View3DAxes.YGrid = 'on';
            app.View3DAxes.ZGrid = 'on';
            app.View3DAxes.Box = 'on';
            view(app.View3DAxes, 45, 30);
            axis(app.View3DAxes, 'equal');
        end
        
        function loadDefaultScenario(app)
            % 加载默认场景
            
            app.OptimizationData.env = initializeEnvironment();
            app.OptimizationData.crane = initializeCraneModel();
            
            % 在3D视图中显示
            if app.ShowObstaclesCheckBox.Value
                drawObstaclesInAxes(app.View3DAxes, app.OptimizationData.env);
            end
        end
    end
    
    % 组件初始化
    methods (Access = private)
        
        function createComponents(app)
            % 创建UI组件
            
            % 创建主窗口
            app.UIFigure = uifigure('Visible', 'off');
            app.UIFigure.Position = [100 100 1600 900];
            app.UIFigure.Name = '双吊桥架桥机时空耦合优化系统';
            app.UIFigure.CloseRequestFcn = createCallbackFcn(app, @closeRequestFcn, true);
            
            % 创建网格布局
            app.GridLayout = uigridlayout(app.UIFigure);
            app.GridLayout.ColumnWidth = {300, '1x', 300};
            app.GridLayout.RowHeight = {'1x', 300, 30};
            
            % 创建控制面板
            createControlPanel(app);
            
            % 创建可视化面板
            createVisualizationPanel(app);
            
            % 创建监控面板
            createMonitoringPanel(app);
            
            % 创建图表面板
            createChartsPanel(app);
            
            % 创建状态栏
            app.StatusLabel = uilabel(app.GridLayout);
            app.StatusLabel.Layout.Row = 3;
            app.StatusLabel.Layout.Column = [1 3];
            app.StatusLabel.Text = '准备就绪';
            app.StatusLabel.FontSize = 12;
        end
        
        function createControlPanel(app)
            % 创建控制面板
            
            app.ControlPanel = uipanel(app.GridLayout);
            app.ControlPanel.Title = '控制面板';
            app.ControlPanel.Layout.Row = 1;
            app.ControlPanel.Layout.Column = 1;
            
            g = uigridlayout(app.ControlPanel);
            g.RowHeight = repmat({30}, 1, 15);
            g.ColumnWidth = {'1x'};
            
            % 构件类型选择
            lbl = uilabel(g);
            lbl.Text = '构件类型:';
            lbl.Layout.Row = 1;
            
            app.ComponentTypeDropDown = uidropdown(g);
            app.ComponentTypeDropDown.Items = {'墩柱', '盖梁', '梁片'};
            app.ComponentTypeDropDown.ItemsData = {'column', 'cap_beam', 'beam'};
            app.ComponentTypeDropDown.Value = 'beam';
            app.ComponentTypeDropDown.Layout.Row = 2;
            
            % 优化模式选择
            lbl = uilabel(g);
            lbl.Text = '优化模式:';
            lbl.Layout.Row = 3;
            
            app.OptimizationModeDropDown = uidropdown(g);
            app.OptimizationModeDropDown.Items = {'快速', '平衡', '高质量'};
            app.OptimizationModeDropDown.ItemsData = {'fast', 'balanced', 'quality'};
            app.OptimizationModeDropDown.Value = 'balanced';
            app.OptimizationModeDropDown.Layout.Row = 4;
            
            % 控制按钮
            app.StartButton = uibutton(g, 'push');
            app.StartButton.Text = '开始优化';
            app.StartButton.Layout.Row = 5;
            app.StartButton.ButtonPushedFcn = createCallbackFcn(app, @StartButtonPushed, true);
            app.StartButton.BackgroundColor = [0.3, 0.7, 0.3];
            
            app.StopButton = uibutton(g, 'push');
            app.StopButton.Text = '停止';
            app.StopButton.Layout.Row = 6;
            app.StopButton.ButtonPushedFcn = createCallbackFcn(app, @StopButtonPushed, true);
            app.StopButton.BackgroundColor = [0.7, 0.3, 0.3];
            
            app.PauseButton = uibutton(g, 'push');
            app.PauseButton.Text = '暂停';
            app.PauseButton.Layout.Row = 7;
            app.PauseButton.ButtonPushedFcn = createCallbackFcn(app, @PauseButtonPushed, true);
            
            % 配置按钮
            app.LoadConfigButton = uibutton(g, 'push');
            app.LoadConfigButton.Text = '加载配置';
            app.LoadConfigButton.Layout.Row = 9;
            app.LoadConfigButton.ButtonPushedFcn = createCallbackFcn(app, @LoadConfigButtonPushed, true);
            
            app.SaveConfigButton = uibutton(g, 'push');
            app.SaveConfigButton.Text = '保存配置';
            app.SaveConfigButton.Layout.Row = 10;
            app.SaveConfigButton.ButtonPushedFcn = createCallbackFcn(app, @SaveConfigButtonPushed, true);
            
            % 参数面板
            createParametersPanel(app, g);
        end
        
        function createParametersPanel(app, parentGrid)
            % 创建参数面板
            
            app.ParametersPanel = uipanel(parentGrid);
            app.ParametersPanel.Title = '优化参数';
            app.ParametersPanel.Layout.Row = [12 15];
            
            g = uigridlayout(app.ParametersPanel);
            g.RowHeight = repmat({25}, 1, 6);
            g.ColumnWidth = {'1x', '1x'};
            
            % 种群大小
            lbl = uilabel(g);
            lbl.Text = '种群大小:';
            lbl.Layout.Row = 1;
            lbl.Layout.Column = 1;
            
            app.PopulationSizeSpinner = uispinner(g);
            app.PopulationSizeSpinner.Value = 100;
            app.PopulationSizeSpinner.Limits = [20, 500];
            app.PopulationSizeSpinner.Step = 10;
            app.PopulationSizeSpinner.Layout.Row = 1;
            app.PopulationSizeSpinner.Layout.Column = 2;
            
            % 最大代数
            lbl = uilabel(g);
            lbl.Text = '最大代数:';
            lbl.Layout.Row = 2;
            lbl.Layout.Column = 1;
            
            app.MaxGenerationsSpinner = uispinner(g);
            app.MaxGenerationsSpinner.Value = 50;
            app.MaxGenerationsSpinner.Limits = [10, 200];
            app.MaxGenerationsSpinner.Step = 5;
            app.MaxGenerationsSpinner.Layout.Row = 2;
            app.MaxGenerationsSpinner.Layout.Column = 2;
            
            % 权重滑块
            lbl = uilabel(g);
            lbl.Text = '时间权重:';
            lbl.Layout.Row = 3;
            lbl.Layout.Column = 1;
            
            app.TimeWeightSlider = uislider(g);
            app.TimeWeightSlider.Limits = [0, 1];
            app.TimeWeightSlider.Value = 0.4;
            app.TimeWeightSlider.Layout.Row = 3;
            app.TimeWeightSlider.Layout.Column = 2;
            app.TimeWeightSlider.ValueChangedFcn = createCallbackFcn(app, @WeightSliderChanged, true);
            
            lbl = uilabel(g);
            lbl.Text = '能耗权重:';
            lbl.Layout.Row = 4;
            lbl.Layout.Column = 1;
            
            app.EnergyWeightSlider = uislider(g);
            app.EnergyWeightSlider.Limits = [0, 1];
            app.EnergyWeightSlider.Value = 0.3;
            app.EnergyWeightSlider.Layout.Row = 4;
            app.EnergyWeightSlider.Layout.Column = 2;
            app.EnergyWeightSlider.ValueChangedFcn = createCallbackFcn(app, @WeightSliderChanged, true);
            
            lbl = uilabel(g);
            lbl.Text = '稳定性权重:';
            lbl.Layout.Row = 5;
            lbl.Layout.Column = 1;
            
            app.StabilityWeightSlider = uislider(g);
            app.StabilityWeightSlider.Limits = [0, 1];
            app.StabilityWeightSlider.Value = 0.3;
            app.StabilityWeightSlider.Layout.Row = 5;
            app.StabilityWeightSlider.Layout.Column = 2;
            app.StabilityWeightSlider.ValueChangedFcn = createCallbackFcn(app, @WeightSliderChanged, true);
        end
        
        function createVisualizationPanel(app)
            % 创建可视化面板
            
            app.VisualizationPanel = uipanel(app.GridLayout);
            app.VisualizationPanel.Title = '3D可视化';
            app.VisualizationPanel.Layout.Row = 1;
            app.VisualizationPanel.Layout.Column = 2;
            
            g = uigridlayout(app.VisualizationPanel);
            g.RowHeight = {'1x', 40};
            g.ColumnWidth = {'1x'};
            
            % 3D坐标轴
            app.View3DAxes = uiaxes(g);
            app.View3DAxes.Layout.Row = 1;
            app.View3DAxes.Layout.Column = 1;
            
            % 控制面板
            controlPanel = uipanel(g);
            controlPanel.Layout.Row = 2;
            
            cg = uigridlayout(controlPanel);
            cg.RowHeight = {'1x'};
            cg.ColumnWidth = {150, 150, 150, '1x', 100};
            cg.Padding = [5 5 5 5];
            
            app.ShowTrajectoryCheckBox = uicheckbox(cg);
            app.ShowTrajectoryCheckBox.Text = '显示轨迹';
            app.ShowTrajectoryCheckBox.Value = true;
            app.ShowTrajectoryCheckBox.Layout.Column = 1;
            
            app.ShowObstaclesCheckBox = uicheckbox(cg);
            app.ShowObstaclesCheckBox.Text = '显示障碍物';
            app.ShowObstaclesCheckBox.Value = true;
            app.ShowObstaclesCheckBox.Layout.Column = 2;
            
            lbl = uilabel(cg);
            lbl.Text = '动画速度:';
            lbl.Layout.Column = 3;
            
            app.AnimationSpeedSlider = uislider(cg);
            app.AnimationSpeedSlider.Limits = [0.1, 5];
            app.AnimationSpeedSlider.Value = 1;
            app.AnimationSpeedSlider.Layout.Column = 4;
        end
        
        function createMonitoringPanel(app)
            % 创建监控面板
            
            app.MonitoringPanel = uipanel(app.GridLayout);
            app.MonitoringPanel.Title = '实时监控';
            app.MonitoringPanel.Layout.Row = 1;
            app.MonitoringPanel.Layout.Column = 3;
            
            g = uigridlayout(app.MonitoringPanel);
            g.RowHeight = {80, 40, 40, 40, '1x'};
            g.ColumnWidth = {'1x'};
            g.Padding = [10 10 10 10];
            
            % 进度条
            app.ProgressGauge = uigauge(g, 'linear');
            app.ProgressGauge.Layout.Row = 1;
            app.ProgressGauge.Limits = [0, 100];
            app.ProgressGauge.MajorTicks = [0, 25, 50, 75, 100];
            app.ProgressGauge.Value = 0;
            
            % 状态标签
            app.GenerationLabel = uilabel(g);
            app.GenerationLabel.Text = '代数: 0 / 0';
            app.GenerationLabel.Layout.Row = 2;
            app.GenerationLabel.FontSize = 14;
            
            app.BestFitnessLabel = uilabel(g);
            app.BestFitnessLabel.Text = '最优: --';
            app.BestFitnessLabel.Layout.Row = 3;
            app.BestFitnessLabel.FontSize = 12;
            
            app.TimeElapsedLabel = uilabel(g);
            app.TimeElapsedLabel.Text = '用时: 0秒';
            app.TimeElapsedLabel.Layout.Row = 4;
            app.TimeElapsedLabel.FontSize = 12;
            
            % 指标面板
            metricsPanel = uipanel(g);
            metricsPanel.Title = '实时指标';
            metricsPanel.Layout.Row = 5;
            
            app.MetricsAxes = uiaxes(metricsPanel);
        end
        
        function createChartsPanel(app)
            % 创建图表面板
            
            app.ChartsPanel = uipanel(app.GridLayout);
            app.ChartsPanel.Title = '优化图表';
            app.ChartsPanel.Layout.Row = 2;
            app.ChartsPanel.Layout.Column = [1 3];
            
            g = uigridlayout(app.ChartsPanel);
            g.RowHeight = {'1x'};
            g.ColumnWidth = {'1x', '1x', '1x'};
            
            % 收敛曲线
            app.ConvergenceAxes = uiaxes(g);
            app.ConvergenceAxes.Layout.Column = 1;
            title(app.ConvergenceAxes, '收敛曲线');
            
            % Pareto前沿
            app.ParetoAxes = uiaxes(g);
            app.ParetoAxes.Layout.Column = 2;
            title(app.ParetoAxes, 'Pareto前沿');
            
            % 性能指标
            panel = uipanel(g);
            panel.Layout.Column = 3;
            panel.Title = '关键指标';
        end
    end
    
    % App创建和删除
    methods (Access = public)
        
        % 构造应用
        function app = realtime_monitor_gui
            
            % 创建UIFigure和组件
            createComponents(app);
            
            % 在创建UIFigure及其所有组件后注册应用
            registerApp(app, app.UIFigure);
            
            % 执行启动函数
            runStartupFcn(app, @startupFcn);
            
            if nargout == 0
                clear app
            end
        end
        
        % 删除前的代码
        function delete(app)
            
            % 删除应用
            delete(app.UIFigure);
        end
    end
end

%% 辅助函数

function results = runOptimizationAsync(config)
    % 异步运行优化
    
    % 这里调用主优化函数
    % 注意：这是简化版本，实际应调用完整的优化系统
    
    % 初始化
    env = initializeEnvironment();
    crane = initializeCraneModel();
    gprModel = initializeGPRModel();
    
    % 运行优化
    % ... 优化代码 ...
    
    % 返回结果
    results = struct();
    results.success = true;
    results.trajectory = struct(); % 填充轨迹数据
    results.performance = struct(); % 填充性能数据
end

function drawObstaclesInAxes(ax, env)
    % 在指定坐标轴中绘制障碍物
    
    hold(ax, 'on');
    
    for i = 1:length(env.obstacles)
        obs = env.obstacles(i);
        
        if strcmp(obs.type, 'box')
            [X, Y, Z] = createBox(obs.center, obs.size);
            surf(ax, X, Y, Z, 'FaceColor', [0.8, 0.2, 0.2], ...
                 'EdgeColor', 'none', 'FaceAlpha', 0.5);
        end
    end
    
    hold(ax, 'off');
end


