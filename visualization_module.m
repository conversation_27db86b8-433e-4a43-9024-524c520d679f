%% 结果可视化模块
% 双吊桥架桥机优化结果的3D可视化和性能分析

function visualizeResults(trajectory, performance, env, crane)
    % 主可视化函数
    
    % 创建图形窗口
    figure('Name', '双吊桥架桥机时空耦合优化结果', ...
           'Position', [100, 100, 1600, 900]);
    
    % 子图1: 3D轨迹动画
    subplot(2, 3, [1, 4]);
    animate3DTrajectory(trajectory, env, crane);
    
    % 子图2: 时间历程曲线
    subplot(2, 3, 2);
    plotTimeHistory(trajectory);
    
    % 子图3: 性能指标
    subplot(2, 3, 3);
    plotPerformanceMetrics(performance);
    
    % 子图4: 摆角分析
    subplot(2, 3, 5);
    plotSwingAnalysis(trajectory, performance);
    
    % 子图5: 能耗分析
    subplot(2, 3, 6);
    plotEnergyAnalysis(trajectory, crane);
    
    % 保存图形
    saveas(gcf, 'optimization_results.png');
end

function animate3DTrajectory(trajectory, env, crane)
    % 3D轨迹动画
    
    hold on;
    grid on;
    axis equal;
    
    % 设置视角
    view(45, 30);
    xlabel('X (m)');
    ylabel('Y (m)');
    zlabel('Z (m)');
    title('双吊桥架桥机3D轨迹');
    
    % 绘制工作空间边界
    drawWorkspace(env.workspace);
    
    % 绘制障碍物
    for i = 1:length(env.obstacles)
        drawObstacle(env.obstacles(i));
    end
    
    % 绘制起始和目标位置
    plot3(env.start_pos(1), env.start_pos(2), env.start_pos(3), ...
          'go', 'MarkerSize', 10, 'MarkerFaceColor', 'g', 'DisplayName', '起始位置');
    plot3(env.goal_pos(1), env.goal_pos(2), env.goal_pos(3), ...
          'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'r', 'DisplayName', '目标位置');
    
    % 绘制轨迹
    plot3(trajectory.hook1(:, 1), trajectory.hook1(:, 2), trajectory.hook1(:, 3), ...
          'b-', 'LineWidth', 2, 'DisplayName', '吊钩1轨迹');
    plot3(trajectory.hook2(:, 1), trajectory.hook2(:, 2), trajectory.hook2(:, 3), ...
          'r-', 'LineWidth', 2, 'DisplayName', '吊钩2轨迹');
    plot3(trajectory.load_center(:, 1), trajectory.load_center(:, 2), ...
          trajectory.load_center(:, 3), 'k--', 'LineWidth', 1.5, 'DisplayName', '负载中心');
    
    % 动画显示
    n_frames = min(50, size(trajectory.hook1, 1));
    frame_indices = round(linspace(1, size(trajectory.hook1, 1), n_frames));
    
    % 初始化动画对象
    crane_obj = [];
    load_obj = [];
    cable_obj1 = [];
    cable_obj2 = [];
    
    for i = frame_indices
        % 删除前一帧
        delete(crane_obj);
        delete(load_obj);
        delete(cable_obj1);
        delete(cable_obj2);
        
        % 绘制架桥机
        crane_obj = drawCrane(trajectory, i, crane);
        
        % 绘制负载
        load_obj = drawLoad(trajectory, i, crane);
        
        % 绘制吊索
        [cable_obj1, cable_obj2] = drawCables(trajectory, i, crane);
        
        % 更新标题显示时间
        title(sprintf('双吊桥架桥机3D轨迹 (t = %.1f s)', trajectory.time(i)));
        
        drawnow;
        pause(0.05);
    end
    
    legend('Location', 'best');
    hold off;
end

function drawWorkspace(workspace)
    % 绘制工作空间边界
    
    % 边界顶点
    vertices = [
        workspace(1,1), workspace(2,1), workspace(3,1);
        workspace(1,2), workspace(2,1), workspace(3,1);
        workspace(1,2), workspace(2,2), workspace(3,1);
        workspace(1,1), workspace(2,2), workspace(3,1);
        workspace(1,1), workspace(2,1), workspace(3,2);
        workspace(1,2), workspace(2,1), workspace(3,2);
        workspace(1,2), workspace(2,2), workspace(3,2);
        workspace(1,1), workspace(2,2), workspace(3,2);
    ];
    
    % 边
    edges = [
        1,2; 2,3; 3,4; 4,1;  % 底面
        5,6; 6,7; 7,8; 8,5;  % 顶面
        1,5; 2,6; 3,7; 4,8;  % 垂直边
    ];
    
    % 绘制边框
    for i = 1:size(edges, 1)
        plot3([vertices(edges(i,1), 1), vertices(edges(i,2), 1)], ...
              [vertices(edges(i,1), 2), vertices(edges(i,2), 2)], ...
              [vertices(edges(i,1), 3), vertices(edges(i,2), 3)], ...
              'k:', 'LineWidth', 0.5);
    end
end

function drawObstacle(obstacle)
    % 绘制障碍物
    
    if strcmp(obstacle.type, 'box')
        % 创建立方体
        [X, Y, Z] = createBox(obstacle.center, obstacle.size);
        
        % 绘制表面
        surf(X, Y, Z, 'FaceColor', [0.8, 0.2, 0.2], ...
             'EdgeColor', 'none', 'FaceAlpha', 0.5);
    end
end

function [X, Y, Z] = createBox(center, size)
    % 创建立方体网格
    
    % 顶点
    dx = size(1)/2; dy = size(2)/2; dz = size(3)/2;
    
    % 各面顶点
    vertices = [
        -dx, -dy, -dz;
        dx, -dy, -dz;
        dx, dy, -dz;
        -dx, dy, -dz;
        -dx, -dy, dz;
        dx, -dy, dz;
        dx, dy, dz;
        -dx, dy, dz;
    ] + center;
    
    % 面
    faces = [
        1, 2, 6, 5;  % 前
        4, 3, 7, 8;  % 后
        1, 4, 8, 5;  % 左
        2, 3, 7, 6;  % 右
        1, 2, 3, 4;  % 底
        5, 6, 7, 8;  % 顶
    ];
    
    % 创建网格
    X = zeros(6, 4);
    Y = zeros(6, 4);
    Z = zeros(6, 4);
    
    for i = 1:6
        for j = 1:4
            X(i, j) = vertices(faces(i, j), 1);
            Y(i, j) = vertices(faces(i, j), 2);
            Z(i, j) = vertices(faces(i, j), 3);
        end
    end
end

function crane_obj = drawCrane(trajectory, idx, crane)
    % 绘制架桥机简化模型
    
    % 主梁位置
    main_pos = trajectory.load_center(idx, :) + [0, 0, crane.cable_length + 5];
    
    % 绘制主梁
    beam_length = 80;
    beam_width = 5;
    beam_height = 3;
    
    [X, Y, Z] = createBox(main_pos, [beam_width, beam_length, beam_height]);
    crane_obj = surf(X, Y, Z, 'FaceColor', [0.5, 0.5, 0.5], ...
                     'EdgeColor', 'none', 'FaceAlpha', 0.8);
end

function load_obj = drawLoad(trajectory, idx, crane)
    % 绘制负载
    
    load_center = trajectory.load_center(idx, :);
    load_yaw = trajectory.load_yaw(idx);
    
    % 创建旋转后的负载
    [X, Y, Z] = createBox(load_center, crane.load.dimensions);
    
    % 应用旋转
    R = [cos(load_yaw), -sin(load_yaw), 0;
         sin(load_yaw), cos(load_yaw), 0;
         0, 0, 1];
    
    for i = 1:numel(X)
        point = [X(i); Y(i); Z(i)] - load_center';
        rotated = R * point + load_center';
        X(i) = rotated(1);
        Y(i) = rotated(2);
        Z(i) = rotated(3);
    end
    
    load_obj = surf(X, Y, Z, 'FaceColor', [0.2, 0.6, 0.2], ...
                    'EdgeColor', 'k', 'FaceAlpha', 0.9);
end

function [cable1, cable2] = drawCables(trajectory, idx, crane)
    % 绘制吊索
    
    hook1_pos = trajectory.hook1(idx, 1:3);
    hook2_pos = trajectory.hook2(idx, 1:3);
    
    % 小车位置（简化）
    cart1_pos = hook1_pos + [0, 0, crane.cable_length];
    cart2_pos = hook2_pos + [0, 0, crane.cable_length];
    
    % 绘制吊索
    cable1 = plot3([cart1_pos(1), hook1_pos(1)], ...
                   [cart1_pos(2), hook1_pos(2)], ...
                   [cart1_pos(3), hook1_pos(3)], ...
                   'b-', 'LineWidth', 2);
    
    cable2 = plot3([cart2_pos(1), hook2_pos(1)], ...
                   [cart2_pos(2), hook2_pos(2)], ...
                   [cart2_pos(3), hook2_pos(3)], ...
                   'r-', 'LineWidth', 2);
end

function plotTimeHistory(trajectory)
    % 绘制时间历程曲线
    
    hold on;
    grid on;
    
    % 位置
    plot(trajectory.time, trajectory.hook1(:, 1), 'b-', 'DisplayName', '吊钩1 X');
    plot(trajectory.time, trajectory.hook1(:, 2), 'b--', 'DisplayName', '吊钩1 Y');
    plot(trajectory.time, trajectory.hook1(:, 3), 'b:', 'DisplayName', '吊钩1 Z');
    
    plot(trajectory.time, trajectory.hook2(:, 1), 'r-', 'DisplayName', '吊钩2 X');
    plot(trajectory.time, trajectory.hook2(:, 2), 'r--', 'DisplayName', '吊钩2 Y');
    plot(trajectory.time, trajectory.hook2(:, 3), 'r:', 'DisplayName', '吊钩2 Z');
    
    xlabel('时间 (s)');
    ylabel('位置 (m)');
    title('吊钩位置时间历程');
    legend('Location', 'best');
    hold off;
end

function plotPerformanceMetrics(performance)
    % 绘制性能指标
    
    % 提取指标
    metrics = performance.trajectory_metrics;
    dynamics = performance.dynamics_metrics;
    
    % 创建条形图数据
    categories = {'作业时间', '能耗', '最大摆角', 'RMS摆角差', '同步误差'};
    values = [metrics.time / 100, ... % 归一化显示
              metrics.energy / 1e6, ...
              dynamics.max_swing_angle * 180/pi, ...
              dynamics.rms_swing_diff * 180/pi, ...
              metrics.sync_error * 10];
    
    % 绘制条形图
    bar(values);
    set(gca, 'XTickLabel', categories);
    ylabel('归一化值');
    title('性能指标总览');
    
    % 添加数值标签
    for i = 1:length(values)
        text(i, values(i), sprintf('%.2f', values(i)), ...
             'HorizontalAlignment', 'center', ...
             'VerticalAlignment', 'bottom');
    end
    
    % 添加综合评分
    overall_score = dynamics.stability_score;
    text(0.5, 0.9, sprintf('稳定性评分: %.2f', overall_score), ...
         'Units', 'normalized', 'FontSize', 12, 'FontWeight', 'bold');
end

function plotSwingAnalysis(trajectory, performance)
    % 绘制摆角分析
    
    % 估算摆角
    g = 9.81;
    L = 20; % 假设缆绳长度
    
    % 从速度估算摆角
    swing_angle1 = atan2(vecnorm(trajectory.hook1(:, 4:5), 2, 2), g) * 180/pi;
    swing_angle2 = atan2(vecnorm(trajectory.hook2(:, 4:5), 2, 2), g) * 180/pi;
    
    hold on;
    grid on;
    
    plot(trajectory.time, swing_angle1, 'b-', 'LineWidth', 2, 'DisplayName', '吊钩1摆角');
    plot(trajectory.time, swing_angle2, 'r-', 'LineWidth', 2, 'DisplayName', '吊钩2摆角');
    plot(trajectory.time, abs(swing_angle1 - swing_angle2), 'k--', ...
         'LineWidth', 1.5, 'DisplayName', '摆角差');
    
    % 添加限制线
    yline(5, 'g--', 'LineWidth', 1.5, 'DisplayName', '安全限制');
    
    xlabel('时间 (s)');
    ylabel('摆角 (度)');
    title('负载摆角分析');
    legend('Location', 'best');
    
    % 统计信息
    text(0.02, 0.95, sprintf('最大摆角: %.2f°', max([swing_angle1; swing_angle2])), ...
         'Units', 'normalized', 'VerticalAlignment', 'top');
    text(0.02, 0.88, sprintf('平均摆角差: %.2f°', mean(abs(swing_angle1 - swing_angle2))), ...
         'Units', 'normalized', 'VerticalAlignment', 'top');
    
    hold off;
end

function plotEnergyAnalysis(trajectory, crane)
    % 绘制能耗分析
    
    dt = mean(diff(trajectory.time));
    
    % 计算瞬时功率
    power1 = crane.load.mass * vecnorm(trajectory.hook1(:, 4:6), 2, 2) .* ...
             vecnorm([0, 0, trajectory.hook1(:, 6)], 2, 2);
    power2 = crane.load.mass * vecnorm(trajectory.hook2(:, 4:6), 2, 2) .* ...
             vecnorm([0, 0, trajectory.hook2(:, 6)], 2, 2);
    
    total_power = power1 + power2;
    
    % 累积能耗
    cumulative_energy = cumsum(total_power) * dt / 1e6; % MJ
    
    yyaxis left;
    plot(trajectory.time, total_power / 1000, 'b-', 'LineWidth', 2);
    ylabel('瞬时功率 (kW)');
    
    yyaxis right;
    plot(trajectory.time, cumulative_energy, 'r-', 'LineWidth', 2);
    ylabel('累积能耗 (MJ)');
    
    xlabel('时间 (s)');
    title('能耗分析');
    grid on;
    
    % 统计信息
    text(0.02, 0.95, sprintf('总能耗: %.2f MJ', cumulative_energy(end)), ...
         'Units', 'normalized', 'VerticalAlignment', 'top');
    text(0.02, 0.88, sprintf('平均功率: %.2f kW', mean(total_power) / 1000), ...
         'Units', 'normalized', 'VerticalAlignment', 'top');
end

%% 辅助函数：Pareto前沿分析

function analyzeParetoFront(x_opt, fval_opt)
    % 分析Pareto前沿
    
    figure('Name', 'Pareto前沿分析', 'Position', [200, 200, 1200, 400]);
    
    % 2D投影
    subplot(1, 3, 1);
    scatter(fval_opt(:, 1), fval_opt(:, 2), 50, fval_opt(:, 3), 'filled');
    xlabel('作业时间 (s)');
    ylabel('能耗 (J)');
    title('时间-能耗 Pareto前沿');
    colorbar;
    grid on;
    
    subplot(1, 3, 2);
    scatter(fval_opt(:, 1), -fval_opt(:, 3), 50, fval_opt(:, 2), 'filled');
    xlabel('作业时间 (s)');
    ylabel('稳定性');
    title('时间-稳定性 Pareto前沿');
    colorbar;
    grid on;
    
    subplot(1, 3, 3);
    scatter(fval_opt(:, 2), -fval_opt(:, 3), 50, fval_opt(:, 1), 'filled');
    xlabel('能耗 (J)');
    ylabel('稳定性');
    title('能耗-稳定性 Pareto前沿');
    colorbar;
    grid on;
end