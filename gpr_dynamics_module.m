%% GPR代理模型与动力学评估模块
% 高斯过程回归代理模型和双摆动力学评估

function gprModel = updateGPRModel(gprModel, config)
    % 更新GPR代理模型
    % 选择有前景的解进行精确评估并更新模型
    
    fprintf('更新GPR代理模型...\n');
    
    % 使用采集函数选择新的评估点
    n_new_samples = config.gpr.n_samples;
    X_new = selectNewSamples(gprModel, n_new_samples, config);
    
    % 精确评估新样本
    Y_new = zeros(n_new_samples, 4);
    
    parfor i = 1:n_new_samples
        % 解码染色体
        [anchors, phases, weights] = decodeChromosome(X_new(i, :));
        
        % 精确评估
        Y_new(i, :) = evaluatePreciseWithDynamics(anchors, phases, weights, config);
    end
    
    % 更新训练数据
    gprModel.X_train = [gprModel.X_train; X_new];
    gprModel.Y_train = [gprModel.Y_train; Y_new];
    
    % 重新训练GPR模型
    fprintf('重新训练GPR模型 (样本数: %d)...\n', size(gprModel.X_train, 1));
    
    gprModel.time = fitrgp(gprModel.X_train, gprModel.Y_train(:, 1), ...
        'KernelFunction', 'ardSquaredExponential', ...
        'Standardize', true);
    
    gprModel.energy = fitrgp(gprModel.X_train, gprModel.Y_train(:, 2), ...
        'KernelFunction', 'ardSquaredExponential', ...
        'Standardize', true);
    
    gprModel.stability = fitrgp(gprModel.X_train, gprModel.Y_train(:, 3), ...
        'KernelFunction', 'ardSquaredExponential', ...
        'Standardize', true);
    
    gprModel.feasibility = fitrgp(gprModel.X_train, gprModel.Y_train(:, 4), ...
        'KernelFunction', 'ardSquaredExponential', ...
        'Standardize', true);
    
    gprModel.generation = gprModel.generation + 1;
    
    fprintf('GPR模型更新完成\n');
end

function X_new = selectNewSamples(gprModel, n_samples, config)
    % 使用采集函数选择新的评估点
    % 基于不确定性和期望改进
    
    % 生成候选点
    n_candidates = n_samples * 10;
    X_candidates = generateCandidates(n_candidates, config);
    
    % 计算采集函数值
    acquisition_values = zeros(n_candidates, 1);
    
    for i = 1:n_candidates
        % 预测均值和方差
        [mu_time, sigma_time] = predict(gprModel.time, X_candidates(i, :));
        [mu_energy, sigma_energy] = predict(gprModel.energy, X_candidates(i, :));
        [mu_stability, sigma_stability] = predict(gprModel.stability, X_candidates(i, :));
        [mu_feasibility, sigma_feasibility] = predict(gprModel.feasibility, X_candidates(i, :));
        
        % UCB采集函数
        beta = 2.0; % 探索-利用权衡参数
        ucb_time = mu_time - beta * sigma_time;
        ucb_energy = mu_energy - beta * sigma_energy;
        ucb_stability = -mu_stability - beta * sigma_stability;
        
        % 加权组合
        acquisition_values(i) = 0.4 * ucb_time + 0.3 * ucb_energy + 0.3 * ucb_stability;
        
        % 可行性惩罚
        if mu_feasibility < 0.5
            acquisition_values(i) = acquisition_values(i) + 1e6;
        end
    end
    
    % 选择最佳候选点
    [~, sorted_idx] = sort(acquisition_values);
    X_new = X_candidates(sorted_idx(1:n_samples), :);
end

function X_candidates = generateCandidates(n_candidates, config)
    % 生成候选点
    % 使用拉丁超立方采样确保覆盖
    
    nvars = config.ga.nvars;
    lb = config.ga.lb;
    ub = config.ga.ub;
    
    % 拉丁超立方采样
    X_normalized = lhsdesign(n_candidates, nvars);
    
    % 缩放到实际范围
    X_candidates = zeros(n_candidates, nvars);
    for i = 1:nvars
        X_candidates(:, i) = lb(i) + X_normalized(:, i) * (ub(i) - lb(i));
    end
end

function metrics = evaluatePreciseWithDynamics(anchors, phases, weights, config)
    % 精确评估，包含完整的动力学模拟
    
    % 初始化环境和架桥机
    env = initializeEnvironment();
    crane = initializeCraneModel();
    
    % RRT*路径规划
    path = planRRTStar(anchors, config, env, crane);
    
    if isempty(path)
        metrics = [1e6, 1e6, 0, 0]; % 不可行解
        return;
    end
    
    % QP轨迹优化
    [trajectory, traj_metrics] = optimizeTrajectoryQP(path, phases, weights, config, crane);
    
    % 动力学仿真评估
    dynamics_metrics = simulateDynamics(trajectory, crane);
    
    % 组合指标
    metrics = [
        traj_metrics.time, 
        traj_metrics.energy, 
        dynamics_metrics.stability_score,
        1 % 可行
    ];
end

function dynamics_metrics = simulateDynamics(trajectory, crane)
    % 双摆动力学仿真
    
    dt = 0.01; % 仿真时间步长
    t_final = trajectory.time(end);
    n_steps = ceil(t_final / dt);
    
    % 初始化状态
    % 状态向量: [theta1, phi1, theta2, phi2, dtheta1, dphi1, dtheta2, dphi2]
    % theta: 摆角（相对垂直方向）, phi: 方位角
    state = zeros(n_steps, 8);
    
    % 负载参数
    m = crane.load.mass;
    L1 = crane.cable_length; % 吊钩1缆绳长度
    L2 = crane.cable_length; % 吊钩2缆绳长度
    g = 9.81;
    
    % 阻尼系数
    c = 0.1;
    
    % 时间历程
    time = (0:n_steps-1) * dt;
    
    % 仿真主循环
    for i = 1:n_steps-1
        t = time(i);
        
        % 插值获取吊钩轨迹
        hook1_acc = interpolateTrajectory(trajectory.hook1, t, 'acc');
        hook2_acc = interpolateTrajectory(trajectory.hook2, t, 'acc');
        
        % 当前状态
        theta1 = state(i, 1); phi1 = state(i, 2);
        theta2 = state(i, 3); phi2 = state(i, 4);
        dtheta1 = state(i, 5); dphi1 = state(i, 6);
        dtheta2 = state(i, 7); dphi2 = state(i, 8);
        
        % 双摆动力学方程
        [ddtheta1, ddphi1] = pendulumDynamics(theta1, phi1, dtheta1, dphi1, ...
            hook1_acc, L1, g, c);
        [ddtheta2, ddphi2] = pendulumDynamics(theta2, phi2, dtheta2, dphi2, ...
            hook2_acc, L2, g, c);
        
        % 耦合项（由于负载连接）
        coupling_force = calculateCouplingForce(state(i, :), crane);
        ddtheta1 = ddtheta1 + coupling_force(1);
        ddphi1 = ddphi1 + coupling_force(2);
        ddtheta2 = ddtheta2 + coupling_force(3);
        ddphi2 = ddphi2 + coupling_force(4);
        
        % 更新状态（欧拉积分）
        state(i+1, 1:4) = state(i, 1:4) + state(i, 5:8) * dt;
        state(i+1, 5:8) = state(i, 5:8) + [ddtheta1, ddphi1, ddtheta2, ddphi2] * dt;
    end
    
    % 计算动力学指标
    dynamics_metrics = struct();
    
    % 最大摆角
    max_angle1 = max(sqrt(state(:, 1).^2 + state(:, 2).^2));
    max_angle2 = max(sqrt(state(:, 3).^2 + state(:, 4).^2));
    dynamics_metrics.max_swing_angle = max(max_angle1, max_angle2);
    
    % RMS摆角差
    angle_diff = sqrt((state(:, 1) - state(:, 3)).^2 + (state(:, 2) - state(:, 4)).^2);
    dynamics_metrics.rms_swing_diff = rms(angle_diff);
    
    % 摆动能量
    kinetic_energy = 0.5 * m * L1^2 * (state(:, 5).^2 + state(:, 6).^2) + ...
                     0.5 * m * L2^2 * (state(:, 7).^2 + state(:, 8).^2);
    dynamics_metrics.swing_energy = mean(kinetic_energy);
    
    % 综合稳定性评分
    dynamics_metrics.stability_score = calculateStabilityScore(dynamics_metrics);
end

function [ddtheta, ddphi] = pendulumDynamics(theta, phi, dtheta, dphi, ...
    hook_acc, L, g, c)
    % 单摆动力学方程
    
    % 分解加速度
    ax = hook_acc(1);
    ay = hook_acc(2);
    az = hook_acc(3);
    
    % 球面摆动力学
    ddtheta = -(g/L) * sin(theta) - (c/L) * dtheta + ...
              (ax * cos(phi) + ay * sin(phi)) * cos(theta) / L;
    
    ddphi = -(ax * sin(phi) - ay * cos(phi)) / (L * sin(theta + eps)) - ...
            (c/L) * dphi;
end

function coupling = calculateCouplingForce(state, crane)
    % 计算耦合力
    % 简化模型：基于负载刚性约束
    
    theta1 = state(1); phi1 = state(2);
    theta2 = state(3); phi2 = state(4);
    
    % 负载扭转刚度
    k_twist = 1000; % N*m/rad
    
    % 角度差异
    delta_theta = theta2 - theta1;
    delta_phi = phi2 - phi1;
    
    % 耦合力矩
    M_theta = k_twist * delta_theta;
    M_phi = k_twist * delta_phi;
    
    % 转换为角加速度
    I = crane.load.mass * crane.cable_length^2; % 转动惯量
    
    coupling = [
        -M_theta / I;
        -M_phi / I;
        M_theta / I;
        M_phi / I;
    ];
end

function acc = interpolateTrajectory(traj_data, t, type)
    % 轨迹插值
    
    % 假设轨迹数据格式: [x, y, z, vx, vy, vz]
    dt = 0.1; % 轨迹时间步长
    idx = floor(t / dt) + 1;
    
    if idx >= size(traj_data, 1)
        idx = size(traj_data, 1) - 1;
    end
    
    if strcmp(type, 'acc')
        % 计算加速度（差分）
        if idx < size(traj_data, 1)
            vel1 = traj_data(idx, 4:6);
            vel2 = traj_data(idx+1, 4:6);
            acc = (vel2 - vel1) / dt;
        else
            acc = [0, 0, 0];
        end
    end
end

function score = calculateStabilityScore(metrics)
    % 计算综合稳定性评分
    
    % 归一化各项指标
    max_angle_norm = 1 / (1 + metrics.max_swing_angle * 10); % 摆角越小越好
    swing_diff_norm = 1 / (1 + metrics.rms_swing_diff * 20); % 同步性越好越好
    energy_norm = 1 / (1 + metrics.swing_energy / 1000); % 能量越小越好
    
    % 加权组合
    score = 0.4 * max_angle_norm + 0.4 * swing_diff_norm + 0.2 * energy_norm;
end

%% 辅助函数：生成控制指令

function [trajectory, performance] = generatePreciseTrajectory(solution, config, env, crane)
    % 从最优解生成精确轨迹
    
    % 解码
    [anchors, phases, weights] = decodeChromosome(solution);
    
    % 规划
    path = planRRTStar(anchors, config, env, crane);
    
    if isempty(path)
        error('无法生成可行路径');
    end
    
    % 优化
    [trajectory, metrics] = optimizeTrajectoryQP(path, phases, weights, config, crane);
    
    % 性能评估
    performance = struct();
    performance.trajectory_metrics = metrics;
    performance.dynamics_metrics = simulateDynamics(trajectory, crane);
end

function control_commands = generateControlCommands(trajectory, crane)
    % 生成控制指令序列
    
    n_steps = length(trajectory.time);
    control_commands = struct();
    
    control_commands.time = trajectory.time;
    control_commands.main_cart_x = zeros(n_steps, 1);
    control_commands.main_cart_y = zeros(n_steps, 1);
    control_commands.small_cart1_pos = zeros(n_steps, 1);
    control_commands.small_cart2_pos = zeros(n_steps, 1);
    control_commands.hoist1_length = zeros(n_steps, 1);
    control_commands.hoist2_length = zeros(n_steps, 1);
    control_commands.rotator_angle = zeros(n_steps, 1);
    
    % 逆运动学计算
    for i = 1:n_steps
        % 吊钩位置
        hook1_pos = trajectory.hook1(i, 1:3);
        hook2_pos = trajectory.hook2(i, 1:3);
        
        % 计算大车位置（x-y平面）
        main_cart_pos = (hook1_pos(1:2) + hook2_pos(1:2)) / 2;
        control_commands.main_cart_x(i) = main_cart_pos(1);
        control_commands.main_cart_y(i) = main_cart_pos(2);
        
        % 计算小车位置（沿梁方向）
        control_commands.small_cart1_pos(i) = hook1_pos(2) - main_cart_pos(2);
        control_commands.small_cart2_pos(i) = hook2_pos(2) - main_cart_pos(2);
        
        % 计算吊索长度
        control_commands.hoist1_length(i) = crane.cable_length - hook1_pos(3);
        control_commands.hoist2_length(i) = crane.cable_length - hook2_pos(3);
        
        % 旋转角度（如果需要）
        if strcmp(crane.component_type, 'cap_beam')
            control_commands.rotator_angle(i) = trajectory.load_yaw(i);
        end
    end
end

function exportControlCommands(commands, filename)
    % 导出控制指令到CSV文件
    
    % 转换为表格
    T = table(commands.time, ...
              commands.main_cart_x, ...
              commands.main_cart_y, ...
              commands.small_cart1_pos, ...
              commands.small_cart2_pos, ...
              commands.hoist1_length, ...
              commands.hoist2_length, ...
              commands.rotator_angle, ...
              'VariableNames', {'Time', 'MainCartX', 'MainCartY', ...
                               'SmallCart1', 'SmallCart2', ...
                               'Hoist1', 'Hoist2', 'Rotator'});
    
    % 写入CSV
    writetable(T, filename);
    
    fprintf('控制指令已导出: %s\n', filename);
    fprintf('总步数: %d, 总时间: %.2f秒\n', height(T), commands.time(end));
end