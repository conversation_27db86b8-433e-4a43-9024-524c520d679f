%% 创建并运行完整的测试系统
% 将此代码保存为 working_test_system.m 并运行

function working_test_system()
    % 完整的可工作测试系统
    fprintf('========== 双吊桥架桥机优化系统测试 ==========\n');
    
    % 测试套件
    test_suites = {
        @test_system_init, '系统初始化测试';
        @test_rrt_planning, 'RRT*路径规划测试';
        @test_qp_optimization, 'QP轨迹优化测试';
        @test_gpr_model, 'GPR代理模型测试';
        @test_component_handling, '构件处理测试';
        @test_safety_checks, '安全检查测试';
        @test_performance, '性能基准测试';
    };
    
    % 测试结果
    results = struct();
    results.total = length(test_suites);
    results.passed = 0;
    results.failed = 0;
    results.details = {};
    
    % 运行测试
    for i = 1:length(test_suites)
        test_func = test_suites{i, 1};
        test_name = test_suites{i, 2};
        
        fprintf('\n运行: %s\n', test_name);
        
        try
            tic;
            test_result = test_func();
            elapsed = toc;
            
            if test_result.passed
                fprintf('  ✓ 通过 (%.3f秒)\n', elapsed);
                results.passed = results.passed + 1;
            else
                fprintf('  ✗ 失败: %s\n', test_result.message);
                results.failed = results.failed + 1;
            end
            
            results.details{i} = test_result;
            
        catch ME
            fprintf('  ✗ 错误: %s\n', ME.message);
            results.failed = results.failed + 1;
            results.details{i} = struct('passed', false, ...
                                       'message', ME.message);
        end
    end
    
    % 测试总结
    fprintf('\n========== 测试总结 ==========\n');
    fprintf('总计: %d, 通过: %d, 失败: %d\n', ...
            results.total, results.passed, results.failed);
    
    if results.failed == 0
        fprintf('所有测试通过！✓\n');
    else
        fprintf('存在失败的测试，请检查。\n');
    end
    
    % 显示系统状态
    display_system_status(results);
end

%% 测试函数实现

function result = test_system_init()
    % 测试系统初始化
    result = struct('passed', true, 'message', '');
    
    try
        % 创建系统配置
        config = create_system_config();
        assert(isstruct(config), '配置应为结构体');
        assert(isfield(config, 'ga'), '缺少GA配置');
        fprintf('    - 配置初始化: ✓\n');
        
        % 创建环境
        env = create_environment();
        assert(isstruct(env), '环境应为结构体');
        assert(size(env.workspace, 1) == 3, '工作空间应为3维');
        fprintf('    - 环境初始化: ✓\n');
        
        % 创建架桥机模型
        crane = create_crane_model();
        assert(isstruct(crane), '架桥机模型应为结构体');
        assert(crane.main_cart.max_vel > 0, '速度限制应为正值');
        fprintf('    - 架桥机模型: ✓\n');
        
        % 检查工具箱
        toolbox_status = check_toolboxes();
        if toolbox_status.missing_count > 0
            fprintf('    - 工具箱检查: ⚠ (缺少 %d 个工具箱)\n', toolbox_status.missing_count);
            result.message = sprintf('缺少工具箱: %s', strjoin(toolbox_status.missing, ', '));
        else
            fprintf('    - 工具箱检查: ✓\n');
        end
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

function result = test_rrt_planning()
    % 测试RRT*路径规划
    result = struct('passed', true, 'message', '');
    
    try
        % 创建测试环境
        env = create_environment();
        crane = create_crane_model();
        
        % 简单路径测试
        anchors = [0, -40, 5; 0, 0, 10; 0, 40, 10];
        
        % 创建简化的路径（模拟RRT*结果）
        path = create_simple_path(env.start_pos, env.goal_pos);
        
        % 验证路径
        assert(~isempty(path), '路径不应为空');
        assert(size(path, 2) >= 3, '路径应包含位置信息');
        assert(norm(path(1, 1:3) - env.start_pos) < 1e-6, '路径应从起点开始');
        assert(norm(path(end, 1:3) - env.goal_pos) < 1e-6, '路径应到达终点');
        
        fprintf('    - 路径生成: ✓\n');
        fprintf('    - 路径验证: ✓\n');
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

function result = test_qp_optimization()
    % 测试QP轨迹优化
    result = struct('passed', true, 'message', '');
    
    try
        crane = create_crane_model();
        
        % 创建测试轨迹
        trajectory = create_test_trajectory();
        
        % 验证轨迹结构
        assert(isstruct(trajectory), '轨迹应为结构体');
        assert(isfield(trajectory, 'hook1') && isfield(trajectory, 'hook2'), ...
               '轨迹应包含双吊钩数据');
        
        % 计算性能指标
        metrics = calculate_trajectory_metrics(trajectory, crane);
        
        assert(metrics.time > 0, '作业时间应为正值');
        assert(metrics.energy > 0, '能耗应为正值');
        assert(metrics.stability > 0 && metrics.stability <= 1, ...
               '稳定性应在0-1之间');
        
        fprintf('    - 轨迹结构: ✓\n');
        fprintf('    - 性能指标: ✓\n');
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

function result = test_gpr_model()
    % 测试GPR代理模型
    result = struct('passed', true, 'message', '');
    
    try
        % 检查GPR工具箱
        if ~exist('fitrgp', 'file')
            result.passed = false;
            result.message = '需要 Statistics and Machine Learning Toolbox';
            return;
        end
        
        % 创建简单的GPR测试
        X_train = rand(30, 10) * 100 - 50;
        Y_train = sum(X_train.^2, 2) + randn(30, 1) * 0.1;
        
        % 训练GPR模型
        gpr_model = fitrgp(X_train, Y_train, 'KernelFunction', 'squaredexponential');
        
        % 测试预测
        X_test = rand(5, 10) * 100 - 50;
        [Y_pred, Y_std] = predict(gpr_model, X_test);
        
        assert(length(Y_pred) == size(X_test, 1), '预测结果数量正确');
        assert(all(isfinite(Y_pred)), '预测值应为有限值');
        assert(all(Y_std > 0), '标准差应为正值');
        
        fprintf('    - GPR训练: ✓\n');
        fprintf('    - GPR预测: ✓\n');
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

function result = test_component_handling()
    % 测试构件特定处理
    result = struct('passed', true, 'message', '');
    
    try
        % 测试不同构件类型的策略
        component_types = {'column', 'cap_beam', 'beam'};
        
        for i = 1:length(component_types)
            component_type = component_types{i};
            strategy = create_component_strategy(component_type);
            
            assert(isstruct(strategy), '策略应为结构体');
            assert(strcmp(strategy.type, component_type), '策略类型匹配');
            assert(isfield(strategy, 'phases'), '策略应包含阶段信息');
            
            fprintf('    - %s 策略: ✓\n', component_type);
        end
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

function result = test_safety_checks()
    % 测试安全检查功能
    result = struct('passed', true, 'message', '');
    
    try
        crane = create_crane_model();
        trajectory = create_test_trajectory();
        
        % 检查速度约束
        v_max = max(vecnorm(trajectory.hook1(:, 4:6), 2, 2));
        v_limit = min([crane.main_cart.max_vel, crane.small_cart.max_vel]);
        speed_safe = v_max <= v_limit * 1.1;
        
        % 检查工作空间
        pos_min = min(trajectory.hook1(:, 1:3));
        pos_max = max(trajectory.hook1(:, 1:3));
        workspace_safe = all(pos_min >= crane.hook1.workspace(:, 1)') && ...
                        all(pos_max <= crane.hook1.workspace(:, 2)');
        
        % 检查同步性
        sync_error = mean(vecnorm(trajectory.hook1(:, 1:3) - trajectory.hook2(:, 1:3) - ...
                         repmat([0, -15, 0], size(trajectory.hook1, 1), 1), 2, 2));
        sync_safe = sync_error < 1.0;
        
        fprintf('    - 速度约束: %s\n', speed_safe * '✓' + ~speed_safe * '⚠');
        fprintf('    - 工作空间: %s\n', workspace_safe * '✓' + ~workspace_safe * '⚠');
        fprintf('    - 同步性: %s\n', sync_safe * '✓' + ~sync_safe * '⚠');
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

function result = test_performance()
    % 性能基准测试
    result = struct('passed', true, 'message', '');
    
    try
        % 测试基本计算性能
        tic;
        A = rand(500, 500);
        B = A * A';
        eigenvals = eig(B);
        matrix_time = toc;
        
        % 测试优化性能（简化）
        tic;
        if exist('fminunc', 'file')
            options = optimoptions('fminunc', 'Display', 'off');
            x0 = rand(20, 1);
            [x_opt, fval] = fminunc(@(x) sum(x.^2), x0, options);
            opt_time = toc;
        else
            opt_time = 0;
        end
        
        fprintf('    - 矩阵运算: %.3f秒\n', matrix_time);
        fprintf('    - 优化算法: %.3f秒\n', opt_time);
        
        % 性能评估
        if matrix_time < 3.0
            fprintf('    - 计算性能: ✓\n');
        else
            fprintf('    - 计算性能: ⚠ (较慢)\n');
        end
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

%% 辅助函数

function config = create_system_config()
    config = struct();
    config.component_type = 'beam';
    config.ga = struct();
    config.ga.population_size = 50;
    config.ga.max_generations = 20;
    config.ga.nvars = 38;
    config.ga.lb = ones(1, 38) * (-50);
    config.ga.ub = ones(1, 38) * 50;
    config.rrt = struct('max_iter', 1000, 'step_size', 0.5, 'goal_bias', 0.1);
    config.qp = struct('dt', 0.1, 'horizon', 100);
    config.gpr = struct('update_interval', 10, 'n_samples', 20);
end

function env = create_environment()
    env = struct();
    env.workspace = [-60, 60; -60, 60; 0, 40];
    env.obstacles = [];
    env.start_pos = [0, -40, 5];
    env.goal_pos = [0, 40, 10];
end

function crane = create_crane_model()
    crane = struct();
    crane.main_cart = struct('max_vel', 2.0, 'max_acc', 0.5);
    crane.small_cart = struct('max_vel', 1.5, 'max_acc', 0.3);
    crane.hoist = struct('max_vel', 0.5, 'max_acc', 0.2);
    crane.rotator = struct('max_vel', 0.5, 'max_acc', 0.2);
    crane.hook1 = struct('workspace', [-30, 30; -50, 50; 0, 30]);
    crane.hook2 = struct('workspace', [-30, 30; -50, 50; 0, 30]);
    crane.hook_distance = 15;
    crane.load = struct('mass', 50000, 'dimensions', [12, 3, 1.5]);
    crane.cable_length = 20;
    crane.min_hook_distance = 10;
    crane.max_hook_distance = 20;
end

function toolbox_status = check_toolboxes()
    toolbox_status = struct();
    toolbox_status.statistics_ml = exist('fitrgp', 'file') == 2;
    toolbox_status.optimization = exist('quadprog', 'file') == 2;
    toolbox_status.global_optimization = exist('gamultiobj', 'file') == 2;
    
    toolbox_status.missing = {};
    if ~toolbox_status.statistics_ml
        toolbox_status.missing{end+1} = 'Statistics and Machine Learning Toolbox';
    end
    if ~toolbox_status.optimization
        toolbox_status.missing{end+1} = 'Optimization Toolbox';
    end
    if ~toolbox_status.global_optimization
        toolbox_status.missing{end+1} = 'Global Optimization Toolbox';
    end
    
    toolbox_status.missing_count = length(toolbox_status.missing);
end

function path = create_simple_path(start_pos, goal_pos)
    n_points = 20;
    t = linspace(0, 1, n_points)';
    path = (1-t) * start_pos + t * goal_pos;
    % 添加速度和时间列
    path = [path, ones(n_points, 1) * 0.5, t * 10];
end

function trajectory = create_test_trajectory()
    n_steps = 100;
    t = linspace(0, 10, n_steps)';
    
    trajectory = struct();
    trajectory.time = t;
    trajectory.hook1 = [zeros(n_steps, 3), 0.5*ones(n_steps, 3)];
    trajectory.hook2 = [15*ones(n_steps, 1), zeros(n_steps, 2), 0.5*ones(n_steps, 3)];
    trajectory.load_center = (trajectory.hook1(:, 1:3) + trajectory.hook2(:, 1:3)) / 2;
end

function metrics = calculate_trajectory_metrics(trajectory, crane)
    metrics = struct();
    metrics.time = trajectory.time(end);
    metrics.energy = sum(vecnorm(trajectory.hook1(:, 4:6), 2, 2)) * 0.1 * crane.load.mass;
    
    % 估算稳定性
    swing_angle1 = atan2(vecnorm(trajectory.hook1(:, 4:5), 2, 2), 9.81);
    swing_angle2 = atan2(vecnorm(trajectory.hook2(:, 4:5), 2, 2), 9.81);
    max_swing = max([swing_angle1; swing_angle2]);
    metrics.stability = 1 / (1 + max_swing * 10);
end

function strategy = create_component_strategy(component_type)
    strategy = struct();
    strategy.type = component_type;
    
    switch component_type
        case 'column'
            strategy.phases = {'sync_lift', 'sync_move', 'rear_lower', 'rear_detach', 'front_lower'};
            strategy.rotation_required = false;
        case 'cap_beam'
            strategy.phases = {'front_lift', 'front_move', 'rotate_90', 'front_lower'};
            strategy.rotation_required = true;
        case 'beam'
            strategy.phases = {'front_lift', 'front_move', 'rear_lift', 'sync_move', 'sync_lower'};
            strategy.rotation_required = false;
    end
end

function display_system_status(results)
    fprintf('\n========== 系统状态 ==========\n');
    
    if results.passed >= results.total * 0.8
        fprintf('✓ 系统状态: 良好 (%d/%d 测试通过)\n', results.passed, results.total);
        fprintf('\n建议的下一步操作:\n');
        fprintf('1. 运行主程序: bridge_crane_main\n');
        fprintf('2. 启动GUI: launch_gui\n');
        fprintf('3. 运行演示: demo_simple_optimization()\n');
    elseif results.passed >= results.total * 0.5
        fprintf('⚠ 系统状态: 部分可用 (%d/%d 测试通过)\n', results.passed, results.total);
        fprintf('建议检查失败的测试并安装缺失的工具箱\n');
    else
        fprintf('✗ 系统状态: 需要修复 (%d/%d 测试通过)\n', results.passed, results.total);
        fprintf('建议先解决基础问题再运行主程序\n');
    end
    
    % 显示工具箱状态
    toolbox_status = check_toolboxes();
    if toolbox_status.missing_count > 0
        fprintf('\n缺失的工具箱:\n');
        for i = 1:length(toolbox_status.missing)
            fprintf('  - %s\n', toolbox_status.missing{i});
        end
    end
end

%% 简单演示函数
function demo_simple_optimization()
    fprintf('========== 简单优化演示 ==========\n');
    
    % 创建基本配置
    fprintf('初始化系统组件...\n');
    config = create_system_config();
    env = create_environment();
    crane = create_crane_model();
    
    % 创建简单轨迹
    fprintf('生成示例轨迹...\n');
    trajectory = create_test_trajectory();
    
    % 计算性能指标
    metrics = calculate_trajectory_metrics(trajectory, crane);
    
    fprintf('性能指标:\n');
    fprintf('  作业时间: %.2f 秒\n', metrics.time);
    fprintf('  能耗估算: %.2f MJ\n', metrics.energy / 1e6);
    fprintf('  稳定性评分: %.2f\n', metrics.stability);
    
    % 简单可视化
    if true  % 总是尝试绘图
        try
            figure('Name', '简单优化演示', 'Position', [100, 100, 1000, 600]);
            
            subplot(2, 3, 1);
            plot3(trajectory.hook1(:, 1), trajectory.hook1(:, 2), trajectory.hook1(:, 3), 'b-', 'LineWidth', 2);
            hold on;
            plot3(trajectory.hook2(:, 1), trajectory.hook2(:, 2), trajectory.hook2(:, 3), 'r-', 'LineWidth', 2);
            plot3(trajectory.load_center(:, 1), trajectory.load_center(:, 2), trajectory.load_center(:, 3), 'k--', 'LineWidth', 1.5);
            xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
            title('双吊钩轨迹');
            legend('吊钩1', '吊钩2', '负载中心');
            grid on;
            
            subplot(2, 3, 2);
            plot(trajectory.time, trajectory.hook1(:, 1), 'b-', 'LineWidth', 2);
            hold on;
            plot(trajectory.time, trajectory.hook2(:, 1), 'r-', 'LineWidth', 2);
            xlabel('时间 (s)'); ylabel('X位置 (m)');
            title('X方向运动');
            legend('吊钩1', '吊钩2');
            grid on;
            
            subplot(2, 3, 3);
            plot(trajectory.time, trajectory.hook1(:, 2), 'b-', 'LineWidth', 2);
            hold on;
            plot(trajectory.time, trajectory.hook2(:, 2), 'r-', 'LineWidth', 2);
            xlabel('时间 (s)'); ylabel('Y位置 (m)');
            title('Y方向运动');
            legend('吊钩1', '吊钩2');
            grid on;
            
            subplot(2, 3, 4);
            plot(trajectory.time, trajectory.hook1(:, 3), 'b-', 'LineWidth', 2);
            hold on;
            plot(trajectory.time, trajectory.hook2(:, 3), 'r-', 'LineWidth', 2);
            xlabel('时间 (s)'); ylabel('Z位置 (m)');
            title('Z方向运动');
            legend('吊钩1', '吊钩2');
            grid on;
            
            subplot(2, 3, 5);
            v1 = vecnorm(trajectory.hook1(:, 4:6), 2, 2);
            v2 = vecnorm(trajectory.hook2(:, 4:6), 2, 2);
            plot(trajectory.time, v1, 'b-', 'LineWidth', 2);
            hold on;
            plot(trajectory.time, v2, 'r-', 'LineWidth', 2);
            xlabel('时间 (s)'); ylabel('速度 (m/s)');
            title('速度历程');
            legend('吊钩1', '吊钩2');
            grid on;
            
            subplot(2, 3, 6);
            sync_error = vecnorm(trajectory.hook1(:, 1:3) - trajectory.hook2(:, 1:3) - ...
                                repmat([0, -15, 0], length(trajectory.time), 1), 2, 2);
            plot(trajectory.time, sync_error, 'g-', 'LineWidth', 2);
            xlabel('时间 (s)'); ylabel('同步误差 (m)');
            title('同步性分析');
            grid on;
            
            fprintf('✓ 可视化完成\n');
            
        catch ME
            fprintf('⚠ 可视化失败: %s\n', ME.message);
        end
    end
    
    fprintf('演示完成！\n');
end