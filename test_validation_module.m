%% 测试与验证模块
% 系统测试、验证和基准测试

function runAllTests()
    % 运行所有测试
    
    fprintf('========== 双吊桥架桥机优化系统测试 ==========\n');
    
    % 测试套件
    test_suites = {
        @testSystemInitialization, '系统初始化测试';
        @testRRTPlanning, 'RRT*路径规划测试';
        @testQPOptimization, 'QP轨迹优化测试';
        @testGPRModel, 'GPR代理模型测试';
        @testComponentHandling, '构件处理测试';
        @testSafetyChecks, '安全检查测试';
        @testPerformance, '性能基准测试';
    };
    
    % 测试结果
    results = struct();
    results.total = length(test_suites);
    results.passed = 0;
    results.failed = 0;
    results.details = {};
    
    % 运行测试
    for i = 1:length(test_suites)
        test_func = test_suites{i, 1};
        test_name = test_suites{i, 2};
        
        fprintf('\n运行: %s\n', test_name);
        
        try
            tic;
            test_result = test_func();
            elapsed = toc;
            
            if test_result.passed
                fprintf('  ✓ 通过 (%.3f秒)\n', elapsed);
                results.passed = results.passed + 1;
            else
                fprintf('  ✗ 失败: %s\n', test_result.message);
                results.failed = results.failed + 1;
            end
            
            results.details{i} = test_result;
            
        catch ME
            fprintf('  ✗ 错误: %s\n', ME.message);
            results.failed = results.failed + 1;
            results.details{i} = struct('passed', false, ...
                                       'message', ME.message);
        end
    end
    
    % 测试总结
    fprintf('\n========== 测试总结 ==========\n');
    fprintf('总计: %d, 通过: %d, 失败: %d\n', ...
            results.total, results.passed, results.failed);
    
    if results.failed == 0
        fprintf('所有测试通过！✓\n');
    else
        fprintf('存在失败的测试，请检查。\n');
    end
end

%% 单元测试函数

function result = testSystemInitialization()
    % 测试系统初始化
    
    result = struct('passed', true, 'message', '');
    
    try
        % 测试配置初始化
        config = initializeSystemConfig();
        assert(isstruct(config), '配置应为结构体');
        assert(isfield(config, 'ga'), '缺少GA配置');
        
        % 测试环境初始化
        env = initializeEnvironment();
        assert(isstruct(env), '环境应为结构体');
        assert(size(env.workspace, 1) == 3, '工作空间应为3维');
        
        % 测试架桥机初始化
        crane = initializeCraneModel();
        assert(isstruct(crane), '架桥机模型应为结构体');
        assert(crane.main_cart.max_vel > 0, '速度限制应为正值');
        
        % 测试GPR初始化
        gprModel = initializeGPRModel();
        assert(isstruct(gprModel), 'GPR模型应为结构体');
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

function result = testRRTPlanning()
    % 测试RRT*路径规划
    
    result = struct('passed', true, 'message', '');
    
    try
        % 准备测试数据
        config = initializeSystemConfig();
        env = initializeEnvironment();
        crane = initializeCraneModel();
        
        % 简单场景测试
        anchors = [0, -40, 5; 0, 0, 10; 0, 40, 10];
        
        % 运行RRT*
        path = planRRTStar(anchors, config, env, crane);
        
        % 验证结果
        assert(~isempty(path), 'RRT*应返回路径');
        assert(size(path, 2) >= 3, '路径应包含位置信息');
        
        % 验证路径有效性
        assert(norm(path(1, 1:3) - env.start_pos) < 1e-3, '路径应从起点开始');
        assert(norm(path(end, 1:3) - env.goal_pos) < 1e-3, '路径应到达终点');
        
        % 测试障碍物场景
        env.obstacles(3) = createObstacle([0, 0, 7], [10, 10, 5], 'box');
        path2 = planRRTStar(anchors, config, env, crane);
        assert(~isempty(path2), 'RRT*应能规避障碍物');
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

function result = testQPOptimization()
    % 测试QP轨迹优化
    
    result = struct('passed', true, 'message', '');
    
    try
        % 准备测试数据
        config = initializeSystemConfig();
        crane = initializeCraneModel();
        
        % 创建简单路径
        path = [
            0, -10, 5, 0.5, 0;
            0, 0, 5, 0.5, 10;
            0, 10, 5, 0.5, 20;
        ];
        
        phases = zeros(10, 2);
        weights = [0.4, 0.3, 0.3];
        
        % 运行QP优化
        [trajectory, metrics] = optimizeTrajectoryQP(path, phases, weights, config, crane);
        
        % 验证轨迹
        assert(isstruct(trajectory), '轨迹应为结构体');
        assert(isfield(trajectory, 'hook1') && isfield(trajectory, 'hook2'), ...
               '轨迹应包含双吊钩数据');
        
        % 验证指标
        assert(isstruct(metrics), '指标应为结构体');
        assert(metrics.time > 0, '作业时间应为正值');
        assert(metrics.energy > 0, '能耗应为正值');
        assert(metrics.stability > 0 && metrics.stability <= 1, ...
               '稳定性应在0-1之间');
        
        % 测试约束满足
        v1_max = max(vecnorm(trajectory.hook1(:, 4:6), 2, 2));
        v2_max = max(vecnorm(trajectory.hook2(:, 4:6), 2, 2));
        v_limit = min([crane.main_cart.max_vel, crane.small_cart.max_vel]);
        
        assert(v1_max <= v_limit * 1.1, '吊钩1速度应满足约束');
        assert(v2_max <= v_limit * 1.1, '吊钩2速度应满足约束');
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

function result = testGPRModel()
    % 测试GPR代理模型
    
    result = struct('passed', true, 'message', '');
    
    try
        % 初始化GPR
        gprModel = initializeGPRModel();
        
        % 测试预测
        test_input = rand(1, 38) * 100 - 50;
        
        [time_pred, ~, time_ci] = predict(gprModel.time, test_input);
        [energy_pred, ~, energy_ci] = predict(gprModel.energy, test_input);
        [stability_pred, ~] = predict(gprModel.stability, test_input);
        [feasibility_pred, ~] = predict(gprModel.feasibility, test_input);
        
        % 验证预测结果
        assert(isfinite(time_pred), '时间预测应为有限值');
        assert(isfinite(energy_pred), '能耗预测应为有限值');
        assert(isfinite(stability_pred), '稳定性预测应为有限值');
        assert(feasibility_pred >= 0 && feasibility_pred <= 1, ...
               '可行性预测应在0-1之间');
        
        % 测试更新
        config = initializeSystemConfig();
        config.gpr.n_samples = 10; % 减少样本数加快测试
        
        gprModel_updated = updateGPRModel(gprModel, config);
        assert(size(gprModel_updated.X_train, 1) > size(gprModel.X_train, 1), ...
               'GPR训练数据应增加');
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

function result = testComponentHandling()
    % 测试构件特定处理
    
    result = struct('passed', true, 'message', '');
    
    try
        % 准备测试数据
        config = initializeSystemConfig();
        crane = initializeCraneModel();
        initial_path = [0, -10, 5; 0, 0, 5; 0, 10, 5];
        
        % 测试墩柱安装
        [traj_column, strategy_column] = planComponentSpecificTrajectory(...
            'column', initial_path, crane, config);
        assert(strcmp(strategy_column.type, 'column'), '策略类型应为column');
        assert(length(strategy_column.phases) == 5, '墩柱应有5个阶段');
        
        % 测试盖梁安装
        [traj_cap, strategy_cap] = planComponentSpecificTrajectory(...
            'cap_beam', initial_path, crane, config);
        assert(strategy_cap.rotation_required, '盖梁应需要旋转');
        
        % 测试梁片安装
        [traj_beam, strategy_beam] = planComponentSpecificTrajectory(...
            'beam', initial_path, crane, config);
        assert(strcmp(strategy_beam.type, 'beam'), '策略类型应为beam');
        
        % 验证轨迹差异
        assert(~isequal(traj_column.hook1, traj_beam.hook1), ...
               '不同构件轨迹应不同');
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

function result = testSafetyChecks()
    % 测试安全检查功能
    
    result = struct('passed', true, 'message', '');
    
    try
        % 准备测试数据
        crane = initializeCraneModel();
        env = initializeEnvironment();
        
        % 创建测试轨迹
        n_steps = 100;
        t = linspace(0, 10, n_steps)';
        
        % 安全轨迹
        safe_trajectory = struct();
        safe_trajectory.time = t;
        safe_trajectory.hook1 = [
            zeros(n_steps, 2), 5*ones(n_steps, 1), ...
            0.5*ones(n_steps, 2), zeros(n_steps, 1)
        ];
        safe_trajectory.hook2 = safe_trajectory.hook1;
        safe_trajectory.hook2(:, 2) = safe_trajectory.hook2(:, 2) + 15;
        safe_trajectory.load_center = (safe_trajectory.hook1(:, 1:3) + ...
                                      safe_trajectory.hook2(:, 1:3)) / 2;
        
        % 测试安全轨迹
        safe = checkInstallationSafety(safe_trajectory, crane, env);
        assert(safe, '安全轨迹应通过检查');
        
        % 创建不安全轨迹（超速）
        unsafe_trajectory = safe_trajectory;
        unsafe_trajectory.hook1(:, 4) = 5; % 超过速度限制
        
        safe = checkDynamicsSafety(unsafe_trajectory, crane);
        assert(~safe, '超速轨迹应被检测为不安全');
        
        % 测试碰撞检测
        collision_trajectory = safe_trajectory;
        collision_trajectory.load_center = repmat([10, 0, 7], n_steps, 1);
        
        collision = checkTrajectoryCollisions(collision_trajectory, env);
        assert(collision, '应检测到碰撞');
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

function result = testPerformance()
    % 性能基准测试
    
    result = struct('passed', true, 'message', '');
    
    try
        % 准备测试环境
        config = initializeSystemConfig();
        config.ga.population_size = 20; % 减小规模加快测试
        config.ga.max_generations = 5;
        env = initializeEnvironment();
        crane = initializeCraneModel();
        
        % 测试GA性能
        tic;
        test_solution = rand(1, config.ga.nvars);
        gprModel = initializeGPRModel();
        fitness = evaluateFitnessWithGPR(test_solution, gprModel, config, env, crane);
        ga_time = toc;
        
        assert(ga_time < 1.0, sprintf('GA评估应在1秒内完成 (实际: %.3f秒)', ga_time));
        
        % 测试RRT*性能
        tic;
        anchors = [0, -40, 5; 0, 0, 10; 0, 40, 10];
        path = planRRTStar(anchors, config, env, crane);
        rrt_time = toc;
        
        assert(rrt_time < 5.0, sprintf('RRT*应在5秒内完成 (实际: %.3f秒)', rrt_time));
        
        % 测试完整优化流程
        tic;
        [x_opt, fval_opt] = runMiniOptimization(config, env, crane);
        total_time = toc;
        
        assert(total_time < 30.0, sprintf('简化优化应在30秒内完成 (实际: %.3f秒)', total_time));
        
        % 性能报告
        result.performance = struct();
        result.performance.ga_eval_time = ga_time;
        result.performance.rrt_planning_time = rrt_time;
        result.performance.total_optimization_time = total_time;
        
    catch ME
        result.passed = false;
        result.message = ME.message;
    end
end

%% 辅助测试函数

function [x_opt, fval_opt] = runMiniOptimization(config, env, crane)
    % 运行简化的优化测试
    
    % 简化GA设置
    ga_options = optimoptions('gamultiobj', ...
        'PopulationSize', 10, ...
        'MaxGenerations', 3, ...
        'Display', 'off');
    
    % 简单适应度函数
    fitnessFcn = @(x) [norm(x(1:3)), sum(x.^2), -min(x)];
    
    nvars = config.ga.nvars;
    lb = config.ga.lb;
    ub = config.ga.ub;
    
    [x_opt, fval_opt] = gamultiobj(fitnessFcn, nvars, [], [], [], [], lb, ub, [], ga_options);
end

%% 验证函数

function validateTrajectory(trajectory, crane)
    % 验证轨迹完整性和一致性
    
    % 检查必需字段
    required_fields = {'time', 'hook1', 'hook2', 'load_center'};
    for i = 1:length(required_fields)
        assert(isfield(trajectory, required_fields{i}), ...
               sprintf('轨迹缺少字段: %s', required_fields{i}));
    end
    
    % 检查维度一致性
    n_steps = length(trajectory.time);
    assert(size(trajectory.hook1, 1) == n_steps, '吊钩1数据长度不一致');
    assert(size(trajectory.hook2, 1) == n_steps, '吊钩2数据长度不一致');
    assert(size(trajectory.load_center, 1) == n_steps, '负载中心数据长度不一致');
    
    % 检查时间单调性
    assert(all(diff(trajectory.time) > 0), '时间应单调递增');
    
    % 检查物理合理性
    dt = mean(diff(trajectory.time));
    for i = 2:n_steps
        % 速度估算
        v1_est = norm(trajectory.hook1(i, 1:3) - trajectory.hook1(i-1, 1:3)) / dt;
        v2_est = norm(trajectory.hook2(i, 1:3) - trajectory.hook2(i-1, 1:3)) / dt;
        
        % 粗略检查（允许一定误差）
        assert(v1_est < crane.main_cart.max_vel * 2, ...
               sprintf('吊钩1速度异常: %.2f m/s', v1_est));
        assert(v2_est < crane.main_cart.max_vel * 2, ...
               sprintf('吊钩2速度异常: %.2f m/s', v2_est));
    end
end

%% 数据生成器

function test_data = generateTestScenarios()
    % 生成测试场景数据
    
    test_data = struct();
    
    % 场景1: 无障碍直线移动
    test_data.scenarios(1) = struct(...
        'name', '直线移动', ...
        'start', [0, -20, 5], ...
        'goal', [0, 20, 5], ...
        'obstacles', [], ...
        'component', 'beam');
    
    % 场景2: 单障碍物规避
    test_data.scenarios(2) = struct(...
        'name', '障碍物规避', ...
        'start', [-20, 0, 5], ...
        'goal', [20, 0, 5], ...
        'obstacles', struct('center', [0, 0, 5], 'size', [8, 8, 10], 'type', 'box'), ...
        'component', 'beam');
    
    % 场景3: 高度变化
    test_data.scenarios(3) = struct(...
        'name', '垂直运动', ...
        'start', [0, 0, 5], ...
        'goal', [0, 0, 25], ...
        'obstacles', [], ...
        'component', 'column');
    
    % 场景4: 复杂路径
    test_data.scenarios(4) = struct(...
        'name', '复杂路径', ...
        'start', [-20, -20, 5], ...
        'goal', [20, 20, 15], ...
        'obstacles', [
            struct('center', [0, -10, 10], 'size', [10, 5, 20], 'type', 'box');
            struct('center', [10, 10, 8], 'size', [5, 10, 15], 'type', 'box')
        ], ...
        'component', 'cap_beam');
end

%% 性能分析

function analyzeOptimizationPerformance(results_history)
    % 分析优化性能
    
    figure('Name', '优化性能分析', 'Position', [100, 100, 1200, 800]);
    
    % 收敛曲线
    subplot(2, 2, 1);
    generations = 1:length(results_history.best_fitness);
    plot(generations, results_history.best_fitness(:, 1), 'b-', 'LineWidth', 2);
    hold on;
    plot(generations, results_history.mean_fitness(:, 1), 'r--', 'LineWidth', 1.5);
    xlabel('代数');
    ylabel('作业时间 (s)');
    title('适应度收敛曲线');
    legend('最优', '平均');
    grid on;
    
    % Pareto前沿演化
    subplot(2, 2, 2);
    scatter(results_history.final_pareto(:, 1), results_history.final_pareto(:, 2), ...
            50, results_history.final_pareto(:, 3), 'filled');
    xlabel('作业时间 (s)');
    ylabel('能耗 (J)');
    title('最终Pareto前沿');
    colorbar;
    grid on;
    
    % 计算时间分析
    subplot(2, 2, 3);
    time_components = [
        results_history.ga_time, ...
        results_history.rrt_time, ...
        results_history.qp_time, ...
        results_history.gpr_time
    ];
    pie(time_components, {'GA', 'RRT*', 'QP', 'GPR'});
    title('计算时间分配');
    
    % 约束违反统计
    subplot(2, 2, 4);
    violations = results_history.constraint_violations;
    bar(categorical({'速度', '加速度', '工作空间', '碰撞'}), violations);
    ylabel('违反次数');
    title('约束违反统计');
    
    % 生成性能报告
    fprintf('\n========== 性能分析报告 ==========\n');
    fprintf('总优化时间: %.2f 秒\n', sum(time_components));
    fprintf('最优解质量:\n');
    fprintf('  作业时间: %.2f 秒\n', min(results_history.final_pareto(:, 1)));
    fprintf('  能耗: %.2f MJ\n', min(results_history.final_pareto(:, 2)) / 1e6);
    fprintf('  稳定性: %.2f\n', max(-results_history.final_pareto(:, 3)));
    fprintf('收敛代数: %d\n', results_history.convergence_generation);
end