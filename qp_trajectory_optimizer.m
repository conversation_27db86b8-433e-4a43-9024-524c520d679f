%% 双吊钩QP轨迹优化模块
% 基于二次规划的双吊钩协同轨迹生成

function [trajectory, metrics] = optimizeTrajectoryQP(path, phases, weights, config, crane)
    % QP轨迹优化主函数
    % 输入:
    %   path - RRT*生成的主体路径
    %   phases - 吊钩相对相位参数
    %   weights - 优化权重 [时间, 能耗, 稳定性]
    %   config - 配置参数
    %   crane - 架桥机模型
    % 输出:
    %   trajectory - 优化后的双吊钩轨迹
    %   metrics - 性能指标
    
    % 参数提取
    dt = config.qp.dt;
    horizon = config.qp.horizon;
    
    % 路径离散化
    [waypoints, T] = discretizePath(path, dt);
    n_steps = size(waypoints, 1);
    
    % 初始化双吊钩轨迹
    hook1_traj = zeros(n_steps, 6); % [x, y, z, vx, vy, vz]
    hook2_traj = zeros(n_steps, 6);
    
    % 构建QP问题
    for k = 1:n_steps-horizon
        % 提取当前时间窗口
        window_waypoints = waypoints(k:k+horizon-1, :);
        
        % 设置QP问题
        [H, f, A, b, Aeq, beq, lb, ub] = setupQPProblem(window_waypoints, ...
            hook1_traj(k, :), hook2_traj(k, :), phases, weights, crane);
        
        % 求解QP (使用quadprog或OSQP)
        options = optimoptions('quadprog', 'Display', 'off', 'Algorithm', 'interior-point-convex');
        
        % Warm start
        if k > 1
            x0 = extractSolutionWindow(hook1_traj(k-1:k+horizon-2, :), ...
                                      hook2_traj(k-1:k+horizon-2, :));
        else
            x0 = [];
        end
        
        [x_opt, ~, exitflag] = quadprog(H, f, A, b, Aeq, beq, lb, ub, x0, options);
        
        if exitflag < 0
            warning('QP求解失败，使用备用策略');
            [hook1_traj(k+1, :), hook2_traj(k+1, :)] = fallbackStrategy(window_waypoints(1, :), crane);
        else
            % 提取优化结果
            [hook1_traj(k+1, :), hook2_traj(k+1, :)] = extractOptimalState(x_opt, k);
        end
        
        % 动力学约束检查
        if ~checkDynamicConstraints(hook1_traj(k:k+1, :), hook2_traj(k:k+1, :), crane)
            warning('动力学约束违反，进行修正');
            [hook1_traj(k+1, :), hook2_traj(k+1, :)] = correctDynamics(hook1_traj(k+1, :), ...
                                                                       hook2_traj(k+1, :), crane);
        end
    end
    
    % 组装完整轨迹
    trajectory = assembleTrajectory(hook1_traj, hook2_traj, waypoints);
    
    % 计算性能指标
    metrics = evaluateTrajectoryMetrics(trajectory, crane);
end

function [H, f, A, b, Aeq, beq, lb, ub] = setupQPProblem(waypoints, ...
    hook1_state, hook2_state, phases, weights, crane)
    % 构建QP问题矩阵
    
    horizon = size(waypoints, 1);
    n_vars = horizon * 12; % 每个时间步: 2个吊钩 × 6个状态
    
    % 初始化矩阵
    H = zeros(n_vars, n_vars);
    f = zeros(n_vars, 1);
    
    % 目标函数: min(时间 + α*能耗 + β*摆角差异)
    % 时间项
    time_weight = weights(1);
    for i = 1:horizon
        idx = (i-1)*12 + 1;
        H(idx:idx+5, idx:idx+5) = H(idx:idx+5, idx:idx+5) + time_weight * eye(6);
        H(idx+6:idx+11, idx+6:idx+11) = H(idx+6:idx+11, idx+6:idx+11) + time_weight * eye(6);
    end
    
    % 能耗项 (速度和加速度的二次项)
    energy_weight = weights(2);
    Q_energy = diag([0, 0, 0, 1, 1, 1]); % 只惩罚速度
    for i = 1:horizon
        idx = (i-1)*12 + 1;
        H(idx:idx+5, idx:idx+5) = H(idx:idx+5, idx:idx+5) + energy_weight * Q_energy;
        H(idx+6:idx+11, idx+6:idx+11) = H(idx+6:idx+11, idx+6:idx+11) + energy_weight * Q_energy;
    end
    
    % 摆角差异惩罚项
    stability_weight = weights(3);
    for i = 1:horizon-1
        idx1 = (i-1)*12 + 1;
        idx2 = i*12 + 1;
        
        % 计算摆角差异的近似二次项
        H_swing = calculateSwingPenalty(crane);
        H(idx1:idx1+11, idx1:idx1+11) = H(idx1:idx1+11, idx1:idx1+11) + stability_weight * H_swing;
    end
    
    % 线性项 (跟踪参考路径)
    for i = 1:horizon
        idx = (i-1)*12 + 1;
        ref_pos = waypoints(i, 1:3)';
        
        % 吊钩1参考位置
        f(idx:idx+2) = -2 * ref_pos - [0; -crane.hook_distance/2; 0];
        % 吊钩2参考位置
        f(idx+6:idx+8) = -2 * ref_pos - [0; crane.hook_distance/2; 0];
    end
    
    % 不等式约束 A*x <= b
    [A, b] = setupInequalityConstraints(horizon, crane);
    
    % 等式约束 Aeq*x = beq (动力学约束)
    [Aeq, beq] = setupEqualityConstraints(horizon, hook1_state, hook2_state, crane);
    
    % 变量边界
    [lb, ub] = setupVariableBounds(horizon, crane);
end

function H_swing = calculateSwingPenalty(crane)
    % 计算摆角差异的惩罚矩阵
    % 基于双摆模型的线性化
    
    % 简化的摆角差异二次型
    H_swing = zeros(12, 12);
    
    % 位置差异导致的摆角差
    pos_penalty = 0.1;
    H_swing(1:3, 7:9) = -pos_penalty * eye(3);
    H_swing(7:9, 1:3) = -pos_penalty * eye(3);
    H_swing(1:3, 1:3) = pos_penalty * eye(3);
    H_swing(7:9, 7:9) = pos_penalty * eye(3);
    
    % 速度差异导致的摆角差
    vel_penalty = 0.05;
    H_swing(4:6, 10:12) = -vel_penalty * eye(3);
    H_swing(10:12, 4:6) = -vel_penalty * eye(3);
    H_swing(4:6, 4:6) = vel_penalty * eye(3);
    H_swing(10:12, 10:12) = vel_penalty * eye(3);
end

function [A, b] = setupInequalityConstraints(horizon, crane)
    % 设置不等式约束
    
    % 估计约束数量
    n_vars = horizon * 12;
    max_constraints = horizon * 20; % 每个时间步最多20个约束
    
    A = [];
    b = [];
    
    % 速度约束
    for i = 1:horizon
        idx = (i-1)*12 + 1;
        
        % 吊钩1速度约束
        A_vel1 = zeros(6, n_vars);
        A_vel1(1:3, idx+3:idx+5) = eye(3);
        A_vel1(4:6, idx+3:idx+5) = -eye(3);
        
        % 吊钩2速度约束
        A_vel2 = zeros(6, n_vars);
        A_vel2(1:3, idx+9:idx+11) = eye(3);
        A_vel2(4:6, idx+9:idx+11) = -eye(3);
        
        A = [A; A_vel1; A_vel2];
        
        v_max = [crane.main_cart.max_vel; crane.small_cart.max_vel; crane.hoist.max_vel];
        b = [b; v_max; v_max; v_max; v_max];
    end
    
    % 工作空间约束
    for i = 1:horizon
        idx = (i-1)*12 + 1;
        
        % 吊钩1工作空间
        A_ws1 = zeros(6, n_vars);
        A_ws1(1:3, idx:idx+2) = eye(3);
        A_ws1(4:6, idx:idx+2) = -eye(3);
        
        % 吊钩2工作空间
        A_ws2 = zeros(6, n_vars);
        A_ws2(1:3, idx+6:idx+8) = eye(3);
        A_ws2(4:6, idx+6:idx+8) = -eye(3);
        
        A = [A; A_ws1; A_ws2];
        
        b_ws1 = [crane.hook1.workspace(:, 2); -crane.hook1.workspace(:, 1)];
        b_ws2 = [crane.hook2.workspace(:, 2); -crane.hook2.workspace(:, 1)];
        b = [b; b_ws1; b_ws2];
    end
    
    % 吊钩间距约束
    for i = 1:horizon
        idx = (i-1)*12 + 1;
        
        % 最小间距约束 (线性化)
        A_dist = zeros(1, n_vars);
        A_dist(1, idx+1) = -1; % y1
        A_dist(1, idx+7) = 1;  % y2
        
        A = [A; A_dist];
        b = [b; -crane.min_hook_distance];
    end
end

function [Aeq, beq] = setupEqualityConstraints(horizon, hook1_state, hook2_state, crane)
    % 设置等式约束 (动力学约束)
    
    n_vars = horizon * 12;
    n_eq = (horizon - 1) * 12 + 12; % 动力学约束 + 初始条件
    
    Aeq = zeros(n_eq, n_vars);
    beq = zeros(n_eq, 1);
    
    % 初始条件约束
    Aeq(1:6, 1:6) = eye(6);
    Aeq(7:12, 7:12) = eye(6);
    beq(1:6) = hook1_state';
    beq(7:12) = hook2_state';
    
    % 动力学约束 (简化的积分器模型)
    dt = 0.1; % 时间步长
    row_idx = 13;
    
    for i = 1:horizon-1
        idx_curr = (i-1)*12 + 1;
        idx_next = i*12 + 1;
        
        % 位置更新: p(k+1) = p(k) + v(k)*dt
        for j = 0:2
            % 吊钩1
            Aeq(row_idx, idx_next+j) = 1;
            Aeq(row_idx, idx_curr+j) = -1;
            Aeq(row_idx, idx_curr+j+3) = -dt;
            row_idx = row_idx + 1;
            
            % 吊钩2
            Aeq(row_idx, idx_next+j+6) = 1;
            Aeq(row_idx, idx_curr+j+6) = -1;
            Aeq(row_idx, idx_curr+j+9) = -dt;
            row_idx = row_idx + 1;
        end
        
        % 速度约束 (考虑加速度限制的简化模型)
        for j = 3:5
            % 吊钩1
            Aeq(row_idx, idx_next+j) = 1;
            Aeq(row_idx, idx_curr+j) = -1;
            % 这里可以添加加速度项
            row_idx = row_idx + 1;
            
            % 吊钩2
            Aeq(row_idx, idx_next+j+6) = 1;
            Aeq(row_idx, idx_curr+j+6) = -1;
            row_idx = row_idx + 1;
        end
    end
end

function [lb, ub] = setupVariableBounds(horizon, crane)
    % 设置变量边界
    n_vars = horizon * 12;
    
    lb = -inf(n_vars, 1);
    ub = inf(n_vars, 1);
    
    % 可以添加更严格的边界
    for i = 1:horizon
        idx = (i-1)*12 + 1;
        
        % 位置边界
        lb(idx:idx+2) = crane.hook1.workspace(:, 1);
        ub(idx:idx+2) = crane.hook1.workspace(:, 2);
        
        lb(idx+6:idx+8) = crane.hook2.workspace(:, 1);
        ub(idx+6:idx+8) = crane.hook2.workspace(:, 2);
        
        % 速度边界
        v_max = [crane.main_cart.max_vel; crane.small_cart.max_vel; crane.hoist.max_vel];
        lb(idx+3:idx+5) = -v_max;
        ub(idx+3:idx+5) = v_max;
        
        lb(idx+9:idx+11) = -v_max;
        ub(idx+9:idx+11) = v_max;
    end
end

function [waypoints, T] = discretizePath(path, dt)
    % 路径离散化
    % path格式: [x, y, z, v, t]
    
    if size(path, 2) < 5
        error('路径格式错误，需要包含速度和时间信息');
    end
    
    total_time = path(end, 5);
    T = 0:dt:total_time;
    n_points = length(T);
    
    waypoints = zeros(n_points, 3);
    
    for i = 1:n_points
        t = T(i);
        
        % 找到对应的路径段
        idx = find(path(:, 5) >= t, 1);
        if isempty(idx)
            idx = size(path, 1);
        end
        
        if idx == 1
            waypoints(i, :) = path(1, 1:3);
        else
            % 线性插值
            t1 = path(idx-1, 5);
            t2 = path(idx, 5);
            alpha = (t - t1) / (t2 - t1);
            
            waypoints(i, :) = path(idx-1, 1:3) * (1 - alpha) + path(idx, 1:3) * alpha;
        end
    end
end

function x0 = extractSolutionWindow(hook1_prev, hook2_prev)
    % 提取前一时刻的解作为warm start
    n = size(hook1_prev, 1);
    x0 = zeros(n * 12, 1);
    
    for i = 1:n
        idx = (i-1)*12 + 1;
        x0(idx:idx+5) = hook1_prev(i, :)';
        x0(idx+6:idx+11) = hook2_prev(i, :)';
    end
end

function [hook1_state, hook2_state] = extractOptimalState(x_opt, step)
    % 从优化结果中提取状态
    idx = 0; % 只取第一个时间步的结果
    hook1_state = x_opt(idx+1:idx+6)';
    hook2_state = x_opt(idx+7:idx+12)';
end

function [hook1_state, hook2_state] = fallbackStrategy(waypoint, crane)
    % QP失败时的备用策略
    % 使用简单的跟踪控制
    
    ref_pos = waypoint(1:3);
    
    % 计算吊钩目标位置
    hook1_target = ref_pos + [0; -crane.hook_distance/2; 0];
    hook2_target = ref_pos + [0; crane.hook_distance/2; 0];
    
    % 简单的P控制
    k_p = 0.5;
    hook1_state = [hook1_target', k_p * ones(1, 3)];
    hook2_state = [hook2_target', k_p * ones(1, 3)];
end

function valid = checkDynamicConstraints(hook1_traj, hook2_traj, crane)
    % 检查动力学约束
    
    % 检查加速度约束
    dt = 0.1;
    acc1 = diff(hook1_traj(:, 4:6)) / dt;
    acc2 = diff(hook2_traj(:, 4:6)) / dt;
    
    a_max = [crane.main_cart.max_acc; crane.small_cart.max_acc; crane.hoist.max_acc];
    
    valid = all(abs(acc1) <= a_max') && all(abs(acc2) <= a_max');
    
    % 检查负载摆角
    if isfield(crane, 'max_swing_angle')
        % 计算摆角 (简化模型)
        swing_angle1 = atan2(norm(hook1_traj(end, 4:5)), 9.81);
        swing_angle2 = atan2(norm(hook2_traj(end, 4:5)), 9.81);
        
        valid = valid && (swing_angle1 < crane.max_swing_angle) && ...
                        (swing_angle2 < crane.max_swing_angle);
    end
end

function [hook1_corrected, hook2_corrected] = correctDynamics(hook1_state, hook2_state, crane)
    % 修正违反动力学约束的状态
    
    % 限制速度
    v_max = [crane.main_cart.max_vel; crane.small_cart.max_vel; crane.hoist.max_vel];
    
    hook1_corrected = hook1_state;
    hook2_corrected = hook2_state;
    
    % 速度限幅
    hook1_corrected(4:6) = max(min(hook1_state(4:6), v_max'), -v_max');
    hook2_corrected(4:6) = max(min(hook2_state(4:6), v_max'), -v_max');
    
    % 确保吊钩间距
    current_distance = norm(hook1_corrected(1:3) - hook2_corrected(1:3));
    if current_distance < crane.min_hook_distance
        % 调整位置以满足最小间距
        center = (hook1_corrected(1:3) + hook2_corrected(1:3)) / 2;
        direction = hook2_corrected(1:3) - hook1_corrected(1:3);
        direction = direction / norm(direction);
        
        hook1_corrected(1:3) = center - direction * crane.min_hook_distance / 2;
        hook2_corrected(1:3) = center + direction * crane.min_hook_distance / 2;
    end
end

function trajectory = assembleTrajectory(hook1_traj, hook2_traj, waypoints)
    % 组装完整轨迹
    trajectory = struct();
    
    trajectory.time = (0:size(hook1_traj, 1)-1)' * 0.1; % dt = 0.1
    trajectory.hook1 = hook1_traj;
    trajectory.hook2 = hook2_traj;
    trajectory.reference = waypoints;
    
    % 计算负载中心轨迹
    trajectory.load_center = (hook1_traj(:, 1:3) + hook2_traj(:, 1:3)) / 2;
    
    % 计算负载姿态 (简化为偏航角)
    trajectory.load_yaw = atan2(hook2_traj(:, 2) - hook1_traj(:, 2), ...
                                hook2_traj(:, 1) - hook1_traj(:, 1));
end

function metrics = evaluateTrajectoryMetrics(trajectory, crane)
    % 评估轨迹性能指标
    metrics = struct();
    
    % 作业时间
    metrics.time = trajectory.time(end);
    
    % 能耗估算
    dt = 0.1;
    power1 = sum(vecnorm(trajectory.hook1(:, 4:6), 2, 2).^2) * dt;
    power2 = sum(vecnorm(trajectory.hook2(:, 4:6), 2, 2).^2) * dt;
    metrics.energy = (power1 + power2) * crane.load.mass;
    
    % 稳定性指标
    % 摆角估算
    g = 9.81;
    swing_angles1 = atan2(vecnorm(trajectory.hook1(:, 4:5), 2, 2), g);
    swing_angles2 = atan2(vecnorm(trajectory.hook2(:, 4:5), 2, 2), g);
    
    metrics.max_swing_angle = max([max(swing_angles1), max(swing_angles2)]);
    metrics.rms_swing_diff = rms(swing_angles1 - swing_angles2);
    
    % 同步误差
    sync_error = vecnorm(trajectory.hook1(:, 1:3) - trajectory.hook2(:, 1:3) - ...
                        repmat([0, -crane.hook_distance, 0], size(trajectory.hook1, 1), 1), 2, 2);
    metrics.sync_error = mean(sync_error);
    
    % 综合稳定性分数
    metrics.stability = 1 / (1 + metrics.max_swing_angle + metrics.rms_swing_diff + metrics.sync_error);
end