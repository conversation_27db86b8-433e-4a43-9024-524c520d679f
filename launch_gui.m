
%{
function launch_gui()
    % 启动双吊桥架桥机优化系统GUI
    
    % 检查必需的工具箱
    required_toolboxes = {
        'Optimization Toolbox'
        'Parallel Computing Toolbox'
        'Statistics and Machine Learning Toolbox'
    };
    
    missing = {};
    for i = 1:length(required_toolboxes)
        if ~license('test', required_toolboxes{i})
            missing{end+1} = required_toolboxes{i};
        end
    end
    
    if ~isempty(missing)
        warning('缺少以下工具箱:');
        for i = 1:length(missing)
            fprintf('  - %s\n', missing{i});
        end
        fprintf('某些功能可能无法正常工作。\n');
    end
    
    % 添加路径
    addpath(genpath(pwd));
    
    % 启动GUI
    app = BridgeCraneOptimizationGUI();
    
    % 等待GUI关闭
    waitfor(app.UIFigure);
end
%}