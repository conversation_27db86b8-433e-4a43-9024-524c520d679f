// qp_solver_mex.cpp
// C++ MEX加速的QP求解器，使用OSQP库
// 编译命令: mex -I/path/to/osqp/include -L/path/to/osqp/lib -losqp qp_solver_mex.cpp

#include "mex.h"
#include "matrix.h"
#include <vector>
#include <cmath>
#include <algorithm>
#include <memory>
#include <cstring>

// OSQP相关头文件 (需要单独安装)
extern "C" {
    #include "osqp.h"
}

// 稀疏矩阵转换辅助结构
struct SparseMat {
    std::vector<c_int> row_indices;
    std::vector<c_int> col_indices;
    std::vector<c_float> values;
    c_int m, n, nnz;
};

// 将MATLAB稀疏矩阵转换为OSQP格式
SparseMat matlabToOSQPSparse(const mxArray* mat) {
    SparseMat sparse;
    
    sparse.m = mxGetM(mat);
    sparse.n = mxGetN(mat);
    
    if (mxIsSparse(mat)) {
        mwIndex* jc = mxGetJc(mat);
        mwIndex* ir = mxGetIr(mat);
        double* pr = mxGetPr(mat);
        
        sparse.nnz = jc[sparse.n];
        sparse.row_indices.reserve(sparse.nnz);
        sparse.col_indices.reserve(sparse.nnz);
        sparse.values.reserve(sparse.nnz);
        
        for (mwIndex col = 0; col < sparse.n; col++) {
            for (mwIndex idx = jc[col]; idx < jc[col + 1]; idx++) {
                sparse.row_indices.push_back(ir[idx]);
                sparse.col_indices.push_back(col);
                sparse.values.push_back(pr[idx]);
            }
        }
    } else {
        // 稠密矩阵转稀疏
        double* data = mxGetPr(mat);
        for (c_int col = 0; col < sparse.n; col++) {
            for (c_int row = 0; row < sparse.m; row++) {
                double val = data[col * sparse.m + row];
                if (std::abs(val) > 1e-10) {
                    sparse.row_indices.push_back(row);
                    sparse.col_indices.push_back(col);
                    sparse.values.push_back(val);
                }
            }
        }
        sparse.nnz = sparse.values.size();
    }
    
    return sparse;
}

// 双吊钩轨迹优化QP求解器
class DualHookQPSolver {
private:
    OSQPWorkspace* workspace;
    OSQPData* data;
    OSQPSettings* settings;
    
    // 问题维度
    c_int n_vars;      // 决策变量数
    c_int n_constraints; // 约束数
    c_int horizon;     // 时间步数
    
    // 缓存的矩阵数据
    std::unique_ptr<csc> P_csc;
    std::unique_ptr<csc> A_csc;
    
public:
    DualHookQPSolver() : workspace(nullptr), data(nullptr), settings(nullptr) {
        settings = (OSQPSettings*)c_malloc(sizeof(OSQPSettings));
        osqp_set_default_settings(settings);
        
        // 优化设置
        settings->alpha = 1.6;          // 松弛参数
        settings->rho = 0.1;           // ADMM惩罚参数
        settings->eps_abs = 1e-4;      // 绝对精度
        settings->eps_rel = 1e-4;      // 相对精度
        settings->max_iter = 2000;     // 最大迭代次数
        settings->warm_start = 1;      // 启用warm start
        settings->verbose = 0;         // 静默模式
    }
    
    ~DualHookQPSolver() {
        if (workspace) osqp_cleanup(workspace);
        if (settings) c_free(settings);
        if (data) {
            if (data->q) c_free(data->q);
            if (data->l) c_free(data->l);
            if (data->u) c_free(data->u);
            c_free(data);
        }
    }
    
    // 设置QP问题
    void setupProblem(const SparseMat& H, const double* f, 
                     const SparseMat& A, const double* lb, const double* ub,
                     c_int horizon_size) {
        
        horizon = horizon_size;
        n_vars = H.n;
        n_constraints = A.m;
        
        // 分配数据结构
        data = (OSQPData*)c_malloc(sizeof(OSQPData));
        data->n = n_vars;
        data->m = n_constraints;
        
        // 设置目标函数
        P_csc = createCSCMatrix(H);
        data->P = P_csc.get();
        
        data->q = (c_float*)c_malloc(n_vars * sizeof(c_float));
        std::copy(f, f + n_vars, data->q);
        
        // 设置约束矩阵
        A_csc = createCSCMatrix(A);
        data->A = A_csc.get();
        
        // 设置约束边界
        data->l = (c_float*)c_malloc(n_constraints * sizeof(c_float));
        data->u = (c_float*)c_malloc(n_constraints * sizeof(c_float));
        std::copy(lb, lb + n_constraints, data->l);
        std::copy(ub, ub + n_constraints, data->u);
        
        // 初始化求解器
        c_int exitflag = osqp_setup(&workspace, data, settings);
        if (exitflag != 0) {
            mexErrMsgIdAndTxt("DualHookQP:setup", "OSQP设置失败");
        }
    }
    
    // 求解QP问题
    void solve(double* x_opt, double* fval, c_int* exitflag, 
              const double* x0 = nullptr) {
        
        // Warm start
        if (x0 != nullptr && settings->warm_start) {
            osqp_warm_start_x(workspace, x0);
        }
        
        // 求解
        *exitflag = osqp_solve(workspace);
        
        if (*exitflag == 0) {
            // 成功求解
            std::copy(workspace->solution->x, 
                     workspace->solution->x + n_vars, x_opt);
            
            // 计算目标函数值
            *fval = computeObjectiveValue(x_opt);
        } else {
            // 求解失败
            std::fill(x_opt, x_opt + n_vars, 0.0);
            *fval = INFINITY;
        }
    }
    
    // 更新目标函数
    void updateObjective(const double* f_new) {
        osqp_update_lin_cost(workspace, f_new);
    }
    
    // 更新约束边界
    void updateBounds(const double* l_new, const double* u_new) {
        osqp_update_bounds(workspace, l_new, u_new);
    }
    
private:
    // 创建CSC格式稀疏矩阵
    std::unique_ptr<csc> createCSCMatrix(const SparseMat& sparse) {
        auto mat = std::make_unique<csc>();
        
        mat->m = sparse.m;
        mat->n = sparse.n;
        mat->nz = -1; // CSC格式
        mat->nzmax = sparse.nnz;
        
        // 分配内存
        mat->x = (c_float*)c_malloc(sparse.nnz * sizeof(c_float));
        mat->i = (c_int*)c_malloc(sparse.nnz * sizeof(c_int));
        mat->p = (c_int*)c_malloc((sparse.n + 1) * sizeof(c_int));
        
        // 转换为CSC格式
        std::vector<std::vector<std::pair<c_int, c_float>>> cols(sparse.n);
        
        for (size_t idx = 0; idx < sparse.nnz; idx++) {
            cols[sparse.col_indices[idx]].push_back(
                {sparse.row_indices[idx], sparse.values[idx]});
        }
        
        c_int nnz_count = 0;
        for (c_int col = 0; col < sparse.n; col++) {
            mat->p[col] = nnz_count;
            
            // 排序确保行索引递增
            std::sort(cols[col].begin(), cols[col].end());
            
            for (const auto& entry : cols[col]) {
                mat->i[nnz_count] = entry.first;
                mat->x[nnz_count] = entry.second;
                nnz_count++;
            }
        }
        mat->p[sparse.n] = nnz_count;
        
        return mat;
    }
    
    // 计算目标函数值
    double computeObjectiveValue(const double* x) {
        double obj = 0.0;
        
        // 二次项: x'*P*x
        for (c_int col = 0; col < data->P->n; col++) {
            for (c_int idx = data->P->p[col]; idx < data->P->p[col + 1]; idx++) {
                c_int row = data->P->i[idx];
                obj += x[row] * data->P->x[idx] * x[col];
            }
        }
        obj *= 0.5;
        
        // 线性项: q'*x
        for (c_int i = 0; i < n_vars; i++) {
            obj += data->q[i] * x[i];
        }
        
        return obj;
    }
};

// 构建双吊钩协同约束矩阵
void buildDualHookConstraints(double* A, double* b_lower, double* b_upper,
                            const double* hook_distance, 
                            const double* workspace,
                            c_int horizon, c_int n_constraints) {
    
    c_int row = 0;
    const c_int state_dim = 6; // [x, y, z, vx, vy, vz]
    const c_int n_vars = horizon * 2 * state_dim;
    
    // 吊钩间距约束
    for (c_int t = 0; t < horizon; t++) {
        c_int idx1 = t * 2 * state_dim;
        c_int idx2 = idx1 + state_dim;
        
        // |hook2_y - hook1_y| >= min_distance
        A[row * n_vars + idx2 + 1] = 1.0;  // hook2_y
        A[row * n_vars + idx1 + 1] = -1.0; // hook1_y
        b_lower[row] = *hook_distance;
        b_upper[row] = INFINITY;
        row++;
    }
    
    // 工作空间约束
    for (c_int t = 0; t < horizon; t++) {
        for (c_int hook = 0; hook < 2; hook++) {
            c_int idx = t * 2 * state_dim + hook * state_dim;
            
            // 位置约束
            for (c_int dim = 0; dim < 3; dim++) {
                // 下界
                A[row * n_vars + idx + dim] = 1.0;
                b_lower[row] = workspace[dim * 2];
                b_upper[row] = workspace[dim * 2 + 1];
                row++;
            }
        }
    }
}

// MEX函数入口
void mexFunction(int nlhs, mxArray* plhs[], int nrhs, const mxArray* prhs[]) {
    
    // 检查输入参数
    if (nrhs < 7) {
        mexErrMsgIdAndTxt("DualHookQP:input", 
            "需要7个输入: H, f, A, b_lower, b_upper, x0, params");
    }
    
    // 提取输入
    SparseMat H = matlabToOSQPSparse(prhs[0]);
    double* f = mxGetPr(prhs[1]);
    SparseMat A = matlabToOSQPSparse(prhs[2]);
    double* b_lower = mxGetPr(prhs[3]);
    double* b_upper = mxGetPr(prhs[4]);
    double* x0 = (nrhs > 5 && !mxIsEmpty(prhs[5])) ? mxGetPr(prhs[5]) : nullptr;
    
    // 提取参数
    double* params = mxGetPr(prhs[6]);
    c_int horizon = static_cast<c_int>(params[0]);
    
    // 创建求解器
    DualHookQPSolver solver;
    
    try {
        // 设置问题
        solver.setupProblem(H, f, A, b_lower, b_upper, horizon);
        
        // 分配输出
        plhs[0] = mxCreateDoubleMatrix(H.n, 1, mxREAL); // x_opt
        plhs[1] = mxCreateDoubleScalar(0.0);            // fval
        plhs[2] = mxCreateDoubleScalar(0.0);            // exitflag
        
        double* x_opt = mxGetPr(plhs[0]);
        double* fval = mxGetPr(plhs[1]);
        c_int exitflag;
        
        // 求解
        solver.solve(x_opt, fval, &exitflag, x0);
        *mxGetPr(plhs[2]) = static_cast<double>(exitflag);
        
        // 如果需要，返回更多信息
        if (nlhs > 3) {
            plhs[3] = mxCreateStructMatrix(1, 1, 0, nullptr);
            mxAddField(plhs[3], "iterations");
            mxAddField(plhs[3], "solve_time");
            mxAddField(plhs[3], "status");
            
            // 填充信息（需要从OSQP workspace获取）
            mxSetField(plhs[3], 0, "status", 
                      mxCreateString(exitflag == 0 ? "solved" : "failed"));
        }
        
    } catch (const std::exception& e) {
        mexErrMsgIdAndTxt("DualHookQP:error", e.what());
    }
}

// 编译脚本 (compile_mex.m)
/*
function compile_qp_mex()
    % 编译QP求解器MEX文件
    
    % OSQP路径配置
    osqp_path = '/path/to/osqp';
    
    % 包含路径
    inc_path = ['-I', fullfile(osqp_path, 'include')];
    
    % 库路径
    lib_path = ['-L', fullfile(osqp_path, 'lib')];
    
    % 编译命令
    mex('-v', inc_path, lib_path, '-losqp', 'qp_solver_mex.cpp');
    
    fprintf('QP求解器MEX编译完成\n');
end
*/