%% RRT*路径规划模块 - 集成双吊钩约束
% 改进的RRT*算法，考虑双吊钩可达性和协同约束

function path = planRRTStar(anchors, config, env, crane)
    % RRT*主函数
    % 输入:
    %   anchors - 路径锚点
    %   config - 配置参数
    %   env - 环境模型
    %   crane - 架桥机模型
    % 输出:
    %   path - 优化后的路径
    
    % 初始化
    start_pos = env.start_pos;
    goal_pos = env.goal_pos;
    
    % RRT树初始化
    tree.nodes = start_pos;
    tree.parents = 0;
    tree.costs = 0;
    
    % 参数设置
    max_iter = config.rrt.max_iter;
    step_size = config.rrt.step_size;
    goal_bias = config.rrt.goal_bias;
    neighbor_radius = 2 * step_size;
    
    % 主循环
    for iter = 1:max_iter
        % 目标偏向采样
        if rand() < goal_bias
            sample = goal_pos;
        else
            sample = sampleWithHookConstraint(env.workspace, crane);
        end
        
        % 找最近节点
        [nearest_idx, nearest_node] = findNearestNode(tree, sample);
        
        % 扩展新节点
        new_node = extend(nearest_node, sample, step_size);
        
        % 双吊钩可达性检查
        if ~checkDualHookReachability(new_node, crane)
            continue;
        end
        
        % 碰撞检测
        if checkCollision(nearest_node, new_node, env.obstacles)
            continue;
        end
        
        % 找邻近节点
        near_indices = findNearNodes(tree, new_node, neighbor_radius);
        
        % 选择最佳父节点
        [best_parent_idx, min_cost] = chooseBestParent(tree, near_indices, ...
            nearest_idx, new_node, env.obstacles);
        
        % 添加新节点
        tree = addNode(tree, new_node, best_parent_idx, min_cost);
        new_idx = size(tree.nodes, 1);
        
        % 重连接
        tree = rewire(tree, near_indices, new_idx, new_node, env.obstacles);
        
        % 检查是否到达目标
        if norm(new_node - goal_pos) < step_size
            path = extractPath(tree, new_idx);
            path = [path; goal_pos];
            
            % B样条平滑
            path = smoothPathBSpline(path);
            
            % 速度规划
            path = planVelocityProfile(path, crane);
            
            return;
        end
    end
    
    % 未找到路径
    path = [];
    warning('RRT*未能找到可行路径');
end

function sample = sampleWithHookConstraint(workspace, crane)
    % 考虑双吊钩约束的采样
    valid = false;
    max_attempts = 100;
    attempts = 0;
    
    while ~valid && attempts < max_attempts
        % 随机采样
        sample = [
            workspace(1,1) + rand() * (workspace(1,2) - workspace(1,1));
            workspace(2,1) + rand() * (workspace(2,2) - workspace(2,1));
            workspace(3,1) + rand() * (workspace(3,2) - workspace(3,1));
        ];
        
        % 检查双吊钩是否都能到达
        hook1_pos = sample + [0; -crane.hook_distance/2; 0];
        hook2_pos = sample + [0; crane.hook_distance/2; 0];
        
        valid = isInWorkspace(hook1_pos, crane.hook1.workspace) && ...
                isInWorkspace(hook2_pos, crane.hook2.workspace);
        
        attempts = attempts + 1;
    end
    
    if ~valid
        % 回退到工作空间中心
        sample = mean(workspace, 2);
    end
end

function valid = checkDualHookReachability(pos, crane)
    % 检查双吊钩可达性
    % 考虑前后吊钩的协同约束
    
    % 计算两个吊钩位置
    hook1_pos = pos + [0; -crane.hook_distance/2; 0];
    hook2_pos = pos + [0; crane.hook_distance/2; 0];
    
    % 检查工作空间约束
    valid = isInWorkspace(hook1_pos, crane.hook1.workspace) && ...
            isInWorkspace(hook2_pos, crane.hook2.workspace);
    
    if ~valid
        return;
    end
    
    % 检查运动学约束
    % 确保两个吊钩可以协同移动到目标位置
    [cart1_pos, cart2_pos] = inverseKinematics(hook1_pos, hook2_pos, crane);
    
    if isempty(cart1_pos) || isempty(cart2_pos)
        valid = false;
        return;
    end
    
    % 检查小车间距约束
    cart_distance = norm(cart1_pos - cart2_pos);
    if cart_distance < crane.min_cart_distance || ...
       cart_distance > crane.max_cart_distance
        valid = false;
    end
end

function in = isInWorkspace(pos, workspace)
    % 检查位置是否在工作空间内
    in = all(pos >= workspace(:,1)) && all(pos <= workspace(:,2));
end

function [nearest_idx, nearest_node] = findNearestNode(tree, sample)
    % 找最近节点
    distances = vecnorm(tree.nodes - sample', 2, 2);
    [~, nearest_idx] = min(distances);
    nearest_node = tree.nodes(nearest_idx, :)';
end

function new_node = extend(from_node, to_node, step_size)
    % 扩展节点
    direction = to_node - from_node;
    distance = norm(direction);
    
    if distance <= step_size
        new_node = to_node;
    else
        new_node = from_node + (direction / distance) * step_size;
    end
end

function collision = checkCollision(node1, node2, obstacles)
    % 连续碰撞检测
    collision = false;
    
    % 离散化路径
    n_checks = ceil(norm(node2 - node1) / 0.1);
    for i = 0:n_checks
        t = i / n_checks;
        pos = node1 * (1 - t) + node2 * t;
        
        % 检查每个障碍物
        for j = 1:length(obstacles)
            if isInsideObstacle(pos, obstacles(j))
                collision = true;
                return;
            end
        end
    end
end

function inside = isInsideObstacle(pos, obstacle)
    % 检查点是否在障碍物内
    if strcmp(obstacle.type, 'box')
        inside = all(abs(pos - obstacle.center') <= obstacle.size'/2);
    else
        inside = false;
    end
end

function near_indices = findNearNodes(tree, node, radius)
    % 找邻近节点
    distances = vecnorm(tree.nodes - node', 2, 2);
    near_indices = find(distances <= radius);
end

function [best_parent_idx, min_cost] = chooseBestParent(tree, near_indices, ...
    nearest_idx, new_node, obstacles)
    % 选择最佳父节点
    
    min_cost = tree.costs(nearest_idx) + norm(tree.nodes(nearest_idx, :)' - new_node);
    best_parent_idx = nearest_idx;
    
    for i = 1:length(near_indices)
        idx = near_indices(i);
        
        % 检查连接是否无碰撞
        if checkCollision(tree.nodes(idx, :)', new_node, obstacles)
            continue;
        end
        
        % 计算成本
        cost = tree.costs(idx) + norm(tree.nodes(idx, :)' - new_node);
        
        if cost < min_cost
            min_cost = cost;
            best_parent_idx = idx;
        end
    end
end

function tree = addNode(tree, node, parent_idx, cost)
    % 添加新节点到树
    tree.nodes(end+1, :) = node';
    tree.parents(end+1) = parent_idx;
    tree.costs(end+1) = cost;
end

function tree = rewire(tree, near_indices, new_idx, new_node, obstacles)
    % 重连接操作
    for i = 1:length(near_indices)
        idx = near_indices(i);
        
        if idx == new_idx
            continue;
        end
        
        % 检查通过新节点的路径是否更优
        new_cost = tree.costs(new_idx) + norm(new_node - tree.nodes(idx, :)');
        
        if new_cost < tree.costs(idx)
            % 检查连接是否无碰撞
            if ~checkCollision(new_node, tree.nodes(idx, :)', obstacles)
                tree.parents(idx) = new_idx;
                tree.costs(idx) = new_cost;
            end
        end
    end
end

function path = extractPath(tree, goal_idx)
    % 从树中提取路径
    path = [];
    idx = goal_idx;
    
    while idx > 0
        path = [tree.nodes(idx, :); path];
        idx = tree.parents(idx);
    end
end

function smooth_path = smoothPathBSpline(path)
    % B样条平滑
    if size(path, 1) < 4
        smooth_path = path;
        return;
    end
    
    % 参数化
    t = linspace(0, 1, size(path, 1));
    t_smooth = linspace(0, 1, size(path, 1) * 5);
    
    % B样条插值
    smooth_path = zeros(length(t_smooth), 3);
    for i = 1:3
        pp = spline(t, path(:, i));
        smooth_path(:, i) = ppval(pp, t_smooth);
    end
end

function path = planVelocityProfile(path, crane)
    % 速度规划
    % 使用梯形速度剖面
    
    n_points = size(path, 1);
    
    % 计算路径长度
    distances = vecnorm(diff(path), 2, 2);
    total_distance = sum(distances);
    
    % 时间分配
    velocities = zeros(n_points, 1);
    accelerations = zeros(n_points, 1);
    times = zeros(n_points, 1);
    
    % 梯形速度剖面参数
    v_max = min([crane.main_cart.max_vel, crane.small_cart.max_vel]);
    a_max = min([crane.main_cart.max_acc, crane.small_cart.max_acc]);
    
    % 计算每段的速度和时间
    cumulative_distance = 0;
    for i = 1:n_points-1
        segment_distance = distances(i);
        
        % 加速阶段
        if cumulative_distance < total_distance * 0.3
            velocities(i) = min(v_max, sqrt(2 * a_max * cumulative_distance));
        % 减速阶段
        elseif cumulative_distance > total_distance * 0.7
            remaining_distance = total_distance - cumulative_distance;
            velocities(i) = min(v_max, sqrt(2 * a_max * remaining_distance));
        % 匀速阶段
        else
            velocities(i) = v_max;
        end
        
        times(i+1) = times(i) + segment_distance / max(velocities(i), 0.1);
        cumulative_distance = cumulative_distance + segment_distance;
    end
    
    % 添加速度和时间信息到路径
    path = [path, velocities, times];
end

function [cart1_pos, cart2_pos] = inverseKinematics(hook1_pos, hook2_pos, crane)
    % 双吊钩逆运动学
    % 简化实现，实际应考虑完整的运动学模型
    
    % 假设吊钩垂直悬挂
    cart1_pos = hook1_pos + [0; 0; crane.cable_length];
    cart2_pos = hook2_pos + [0; 0; crane.cable_length];
    
    % 检查小车位置是否可行
    if ~isInWorkspace(cart1_pos, crane.cart_workspace) || ...
       ~isInWorkspace(cart2_pos, crane.cart_workspace)
        cart1_pos = [];
        cart2_pos = [];
    end
end